'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Maximize, Search, Star, Users, Gamepad2, TrendingUp } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';

// 导入游戏数据
import { gamesData, GameCardProps } from '@/data/games';
import OptimizedImage, { imageSizes } from '@/components/OptimizedImage';
import { useMobileDetection, mobileClasses } from '@/components/MobileDetection';
import MobileNavigation from '@/components/MobileNavigation';
import { useBatchImagePreload, useSmartImagePreload } from '@/hooks/useImagePreload';

// 开发环境下导入测试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/imageOptimizationTest');
}

// 游戏分类数据
const categories = [
  { id: 'action', name: { en: 'Action', zh: '动作' }, icon: '🎮' },
  { id: 'adventure', name: { en: 'Adventure', zh: '冒险' }, icon: '🗺️' },
  { id: 'racing', name: { en: 'Racing', zh: '竞速' }, icon: '🏎️' },
  { id: 'shooting', name: { en: 'Shooting', zh: '射击' }, icon: '🔫' },
  { id: 'puzzle', name: { en: 'Puzzle', zh: '益智' }, icon: '🧩' },
  { id: 'strategy', name: { en: 'Strategy', zh: '策略' }, icon: '⚔️' },
  { id: 'sports', name: { en: 'Sports', zh: '体育' }, icon: '⚽' },
  { id: 'simulation', name: { en: 'Simulation', zh: '模拟' }, icon: '🎯' }
];

export default function Home() {
  const [selectedCategory, setSelectedCategory] = useState<string>('action');
  const [selectedGame, setSelectedGame] = useState<GameCardProps | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const t = useTranslations('Home');
  const locale = useLocale();
  const { isMobile, isTablet, deviceType } = useMobileDetection();

  // 获取当前分类的游戏数据
  const currentGames = gamesData[selectedCategory] || [];

  // 预加载首屏图片（前4张）
  const priorityImages = React.useMemo(() => {
    return currentGames.slice(0, 4).map((game, index) => ({
      src: game.image,
      options: {
        priority: index < 2, // 前2张高优先级
        quality: 85,
        width: 320,
        useProxy: true
      }
    }));
  }, [currentGames]);

  // 预加载其他分类的热门图片
  const otherCategoryImages = React.useMemo(() => {
    const images: string[] = [];
    categories.forEach(category => {
      if (category.id !== selectedCategory) {
        const categoryGames = gamesData[category.id] || [];
        images.push(...categoryGames.slice(0, 2).map(game => game.image));
      }
    });
    return images;
  }, [selectedCategory]);

  // 使用批量预加载Hook
  useBatchImagePreload(priorityImages, true);

  // 使用智能预加载Hook预加载其他图片
  useSmartImagePreload(otherCategoryImages, {
    quality: 75,
    width: 320,
    useProxy: true
  });

  // 提前计算当前选中的分类名称
  const currentCategoryName = React.useMemo(() => {
    const category = categories.find(c => c.id === selectedCategory);
    return category ? (category.name[locale as keyof typeof category.name] || category.name['en']) : '';
  }, [selectedCategory, locale]);

  const handleFullscreen = () => {
    const iframe = document.getElementById('game-iframe') as HTMLIFrameElement;
    if (iframe) {
      if (iframe.requestFullscreen) {
        iframe.requestFullscreen();
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section - Mobile/Tablet */}
      <section className={`lg:hidden ${isMobile ? 'py-6' : 'py-8'} bg-secondary/30`}>
        <div className={`container mx-auto ${mobileClasses.mobileSpacing}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-left"
          >
            <h1 className={`${isMobile
              ? 'text-2xl sm:text-3xl'
              : 'text-3xl md:text-4xl'
              } font-bold font-display bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent mb-4`}>
              {t('title')}
            </h1>
            <p className={`${isMobile
              ? 'text-base'
              : 'text-lg'
              } text-muted-foreground mb-6`}>
              {t('description')}
            </p>

            {/* Mobile Categories */}
            <div className="flex flex-wrap gap-2 mb-6">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all ${selectedCategory === category.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                    }`}
                >
                  <span className="mr-1">{category.icon}</span>
                  {category.name[locale as keyof typeof category.name] || category.name['en']}
                </button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Desktop Header - Games For You */}
      <section className="hidden lg:block py-6 px-6 border-b border-border/50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between"
        >
          <div>
            <h1 className="text-2xl font-bold text-foreground mb-1">
              Games For You
            </h1>
            <p className="text-sm text-muted-foreground">
              Discover amazing games curated just for you
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>30M+ players</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Gamepad2 className="h-4 w-4" />
              <span>4,500+ games</span>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Featured Games Section */}
      <section className="py-6 px-6">
          {selectedGame ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl md:text-3xl font-bold">{selectedGame.title[locale]}</h2>
                <button
                  onClick={() => setSelectedGame(null)}
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('backToCategories')}
                </button>
              </div>

              <div className="relative bg-card rounded-lg overflow-hidden shadow-lg">
                <div className="aspect-video w-full relative">
                  <iframe
                    id="game-iframe"
                    src={selectedGame.gameUrl}
                    className="absolute w-full h-full border-none"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; fullscreen"
                    allowFullScreen
                    title={selectedGame.title[locale as keyof typeof selectedGame.title] || selectedGame.title['en']}
                  ></iframe>

                  <div className="absolute bottom-4 right-4 flex space-x-2">
                    <button
                      onClick={handleFullscreen}
                      className="p-2 bg-black/70 text-white rounded-full hover:bg-black/90 transition-all"
                      title={t('fullscreen')}
                    >
                      <Maximize className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Games Grid - Similar to reference image */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-3">
                {currentGames.map((game, index) => (
                  <Link
                    key={game.id}
                    href={`/${locale}/game/${game.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.02 }}
                      className="group cursor-pointer relative"
                    >
                      <div className="aspect-square rounded-lg overflow-hidden bg-secondary relative">
                        {/* Hot/New Badge */}
                        {index < 8 && (
                          <div className="absolute top-1 left-1 z-10">
                            <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded text-[10px] font-bold">
                              {index < 4 ? 'HOT' : 'NEW'}
                            </span>
                          </div>
                        )}

                        <OptimizedImage
                          src={game.image}
                          alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                          width={200}
                          height={200}
                          fill={true}
                          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, (max-width: 1280px) 20vw, (max-width: 1536px) 16vw, 14vw"
                          placeholder="blur"
                          priority={index < 12}
                          quality={index < 12 ? 85 : 75}
                          useProxy={true}
                        />

                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="bg-white/90 backdrop-blur-sm rounded-full p-2">
                              <Gamepad2 className="h-4 w-4 text-primary" />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Game title - only show on hover for cleaner look */}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-b-lg">
                        <h3 className="text-white text-xs font-medium line-clamp-1">
                          {game.title[locale as keyof typeof game.title] || game.title['en']}
                        </h3>
                      </div>
                    </motion.div>
                  </Link>
                ))}
              </div>
            </div>
          )}
      </section>

      {/* Load More Section */}
      <section className="py-8 px-6">
        <div className="text-center">
          <Link
            href={`/${locale}/games/${selectedCategory}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <button className="px-6 py-2.5 bg-secondary hover:bg-secondary/80 text-foreground rounded-lg font-medium transition-all border border-border">
              {t('viewAll') || 'View All Games'}
            </button>
          </Link>
        </div>
      </section>

      {/* Mobile Navigation */}
      {(isMobile || isTablet) && <MobileNavigation />}
    </div>
  );
}
