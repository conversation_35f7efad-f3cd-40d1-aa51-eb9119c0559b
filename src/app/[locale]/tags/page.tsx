'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { Tag, Hash, TrendingUp } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { gamesData } from '@/data/games';
import { gameTags } from '@/components/GameTags';
import Breadcrumb from '@/components/Breadcrumb';

export default function TagsPage() {
  const locale = useLocale();
  const t = useTranslations('Tags');

  // 计算每个标签的游戏数量
  const tagStats = useMemo(() => {
    const stats: Record<string, number> = {};
    
    // 遍历所有游戏，统计标签使用次数
    Object.values(gamesData).forEach(categoryGames => {
      categoryGames.forEach(game => {
        if (game.tags) {
          game.tags.forEach(tag => {
            stats[tag] = (stats[tag] || 0) + 1;
          });
        }
      });
    });

    // 转换为数组并排序
    return Object.entries(stats)
      .map(([tag, count]) => ({
        tag,
        count,
        displayName: gameTags[tag as keyof typeof gameTags]?.[locale as keyof typeof gameTags[keyof typeof gameTags]] || 
                    gameTags[tag as keyof typeof gameTags]?.['en'] || 
                    tag
      }))
      .sort((a, b) => b.count - a.count);
  }, [locale]);

  // 分类标签
  const categoryTags = tagStats.filter(({ tag }) => 
    ['shooting', 'platformer', 'rpg', 'strategy', 'puzzle', 'racing', 'fighting', 'adventure', 'simulation', 'sports'].includes(tag)
  );

  const styleTags = tagStats.filter(({ tag }) => 
    ['retro', 'pixel', '3d', '2d'].includes(tag)
  );

  const gameplayTags = tagStats.filter(({ tag }) => 
    ['multiplayer', 'singleplayer', 'cooperative', 'competitive'].includes(tag)
  );

  const difficultyTags = tagStats.filter(({ tag }) => 
    ['easy', 'medium', 'hard', 'casual', 'hardcore'].includes(tag)
  );

  const specialTags = tagStats.filter(({ tag }) => 
    ['new', 'popular', 'trending', 'featured', 'mobile-friendly', 'no-download', 'browser-game', 'html5'].includes(tag)
  );

  const TagSection = ({ title, tags, icon: Icon }: { title: string; tags: typeof tagStats; icon: any }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-8"
    >
      <div className="flex items-center mb-4">
        <Icon className="h-5 w-5 text-primary mr-2" />
        <h2 className="text-xl font-semibold">{title}</h2>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
        {tags.map(({ tag, count, displayName }) => (
          <Link
            key={tag}
            href={`/${locale}/tags/${tag}`}
            className="group"
          >
            <div className="p-3 rounded-lg border border-border hover:border-primary/50 transition-all duration-200 hover:shadow-md">
              <div className="flex items-center justify-between mb-1">
                <span className="font-medium text-sm group-hover:text-primary transition-colors">
                  {displayName}
                </span>
                <Hash className="h-3 w-3 text-muted-foreground" />
              </div>
              <div className="text-xs text-muted-foreground">
                {count} {t('games') || 'games'}
              </div>
            </div>
          </Link>
        ))}
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb items={[{ label: t('tags') || 'Tags', isCurrentPage: true }]} />
      </div>

      {/* 页面头部 */}
      <div className="bg-secondary/30 py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-left"
          >
            <div className="flex flex-col space-y-4">
              <div className="flex items-center mb-2">
                <Tag className="h-8 w-8 text-primary mr-3" />
                <h1 className="text-3xl md:text-4xl font-bold">
                  {t('title') || 'Game Tags'}
                </h1>
              </div>
              <p className="text-muted-foreground mb-6 max-w-3xl">
                {t('description') || 'Explore games by tags and find exactly what you\'re looking for'}
              </p>
              
              {/* 统计信息 */}
              <div className="flex items-center space-x-8">
                <div className="text-left">
                  <div className="text-2xl font-bold text-primary">{tagStats.length}</div>
                  <div className="text-sm text-muted-foreground">{t('totalTags') || 'Total Tags'}</div>
                </div>
                <div className="text-left">
                  <div className="text-2xl font-bold text-primary">
                    {Object.values(gamesData).reduce((total, games) => total + games.length, 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">{t('totalGames') || 'Total Games'}</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* 标签分类 */}
      <div className="container mx-auto px-4 py-8">
        {/* 热门标签 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-12"
        >
          <div className="flex items-center mb-6">
            <TrendingUp className="h-6 w-6 text-primary mr-3" />
            <h2 className="text-2xl font-bold">{t('popularTags') || 'Popular Tags'}</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {tagStats.slice(0, 8).map(({ tag, count, displayName }) => (
              <Link
                key={tag}
                href={`/${locale}/tags/${tag}`}
                className="group"
              >
                <div className="p-4 rounded-lg border border-border hover:border-primary/50 transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-background to-secondary/20">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold group-hover:text-primary transition-colors">
                      {displayName}
                    </span>
                    <Tag className="h-4 w-4 text-primary" />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {count} {t('games') || 'games'}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </motion.div>

        {/* 游戏类型标签 */}
        {categoryTags.length > 0 && (
          <TagSection 
            title={t('categoryTags') || 'Game Categories'} 
            tags={categoryTags} 
            icon={Tag} 
          />
        )}

        {/* 游戏风格标签 */}
        {styleTags.length > 0 && (
          <TagSection 
            title={t('styleTags') || 'Art Styles'} 
            tags={styleTags} 
            icon={Tag} 
          />
        )}

        {/* 游戏玩法标签 */}
        {gameplayTags.length > 0 && (
          <TagSection 
            title={t('gameplayTags') || 'Gameplay Types'} 
            tags={gameplayTags} 
            icon={Tag} 
          />
        )}

        {/* 难度标签 */}
        {difficultyTags.length > 0 && (
          <TagSection 
            title={t('difficultyTags') || 'Difficulty Levels'} 
            tags={difficultyTags} 
            icon={Tag} 
          />
        )}

        {/* 特殊标签 */}
        {specialTags.length > 0 && (
          <TagSection 
            title={t('specialTags') || 'Special Features'} 
            tags={specialTags} 
            icon={Tag} 
          />
        )}

        {/* 所有标签 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-12"
        >
          <h2 className="text-xl font-semibold mb-4">{t('allTags') || 'All Tags'}</h2>
          <div className="flex flex-wrap gap-2">
            {tagStats.map(({ tag, count, displayName }) => (
              <Link
                key={tag}
                href={`/${locale}/tags/${tag}`}
                className="inline-flex items-center px-3 py-1 rounded-full bg-secondary hover:bg-primary hover:text-primary-foreground transition-colors text-sm"
              >
                {displayName}
                <span className="ml-1 text-xs opacity-70">({count})</span>
              </Link>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
