'use client';

import React, { useState, useMemo, Suspense } from 'react';
import { motion } from 'framer-motion';
import { useTranslations, useLocale } from 'next-intl';
import { Star, TrendingUp, Clock, Gamepad2 } from 'lucide-react';

// 导入优化后的组件和Hook
import { useGameDataContext } from '@/components/GameDataProvider';
import { useGameData, usePopularGames, useNewGames } from '@/hooks/useGameData';
import VirtualizedGameList, { InfiniteScrollGameList } from '@/components/VirtualizedGameList';
import SmartSearch from '@/components/SmartSearch';
import { useMobileDetection } from '@/components/MobileDetection';
import { usePerformanceMonitor } from '@/utils/performanceMonitor';

// 分类配置
const categories = [
  { id: 'action', icon: '🎯', color: 'from-red-500 to-orange-500' },
  { id: 'adventure', icon: '🗺️', color: 'from-green-500 to-teal-500' },
  { id: 'racing', icon: '🏎️', color: 'from-blue-500 to-cyan-500' },
  { id: 'shooting', icon: '🔫', color: 'from-purple-500 to-pink-500' },
  { id: 'puzzle', icon: '🧩', color: 'from-yellow-500 to-orange-500' },
  { id: 'strategy', icon: '⚔️', color: 'from-indigo-500 to-purple-500' },
  { id: 'sports', icon: '⚽', color: 'from-emerald-500 to-green-500' },
  { id: 'simulation', icon: '🎯', color: 'from-slate-500 to-gray-500' },
];

export default function OptimizedHome() {
  const [selectedCategory, setSelectedCategory] = useState<string>('action');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  
  const t = useTranslations('Home');
  const locale = useLocale();
  const { isMobile, isTablet } = useMobileDetection();
  const { recordMetric, startTiming, endTiming } = usePerformanceMonitor();

  // 使用游戏数据Context
  const { 
    initialized, 
    loading, 
    error, 
    stats,
    getGamesByCategory,
    searchGames 
  } = useGameDataContext();

  // 使用优化的Hook获取数据
  const { 
    games: categoryGames, 
    loading: categoryLoading,
    hasMore,
    loadMore 
  } = useGameData({
    category: selectedCategory,
    limit: isMobile ? 12 : 20,
    locale,
  });

  const { games: popularGames, loading: popularLoading } = usePopularGames(8);
  const { games: newGames, loading: newLoading } = useNewGames(8);

  // 处理分类切换
  const handleCategoryChange = (categoryId: string) => {
    startTiming('category-switch');
    setSelectedCategory(categoryId);
    
    // 记录用户行为
    recordMetric({
      name: 'category-switch',
      value: 1,
      category: 'interaction',
      details: { from: selectedCategory, to: categoryId },
    });
    
    endTiming('category-switch', 'interaction');
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    startTiming('search-execution');
    const results = searchGames(query, { locale });
    endTiming('search-execution', 'interaction', { 
      query, 
      resultsCount: results.length 
    });
  };

  // 计算显示的分类
  const displayCategories = useMemo(() => {
    return categories.map(cat => ({
      ...cat,
      name: t(`categories.${cat.id}`) || cat.id,
      gameCount: getGamesByCategory(cat.id).length,
    }));
  }, [t, getGamesByCategory]);

  // 错误状态
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-red-500 text-4xl">⚠️</div>
          <div className="text-lg font-medium">Something went wrong</div>
          <div className="text-sm text-muted-foreground">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 头部搜索区域 */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-12 md:py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              {t('title') || 'Discover Amazing Games'}
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
              {t('subtitle') || 'Play thousands of free games instantly in your browser'}
            </p>
            
            {/* 智能搜索 */}
            <div className="max-w-2xl mx-auto">
              <SmartSearch
                onSearch={handleSearch}
                placeholder={t('searchPlaceholder') || 'Search for games...'}
                showSuggestions={true}
                maxSuggestions={6}
                className="w-full"
              />
            </div>

            {/* 统计信息 */}
            {initialized && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="flex justify-center space-x-8 text-sm text-muted-foreground"
              >
                <div className="flex items-center space-x-1">
                  <Gamepad2 className="h-4 w-4" />
                  <span>{stats.totalGames} Games</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4" />
                  <span>{stats.categories} Categories</span>
                </div>
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4" />
                  <span>Updated Daily</span>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </section>

      {/* 分类导航 */}
      <section className="py-8 border-b border-border">
        <div className="container mx-auto px-4">
          <div className="flex overflow-x-auto space-x-4 pb-4 scrollbar-hide">
            {displayCategories.map((category, index) => (
              <motion.button
                key={category.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => handleCategoryChange(category.id)}
                className={`flex-shrink-0 flex items-center space-x-2 px-4 py-2 rounded-full transition-all ${
                  selectedCategory === category.id
                    ? 'bg-primary text-primary-foreground shadow-lg scale-105'
                    : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                }`}
              >
                <span className="text-lg">{category.icon}</span>
                <span className="font-medium">{category.name}</span>
                <span className="text-xs opacity-75">({category.gameCount})</span>
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* 热门游戏区域 */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-2xl md:text-3xl font-bold flex items-center space-x-2">
                <TrendingUp className="h-6 w-6 text-primary" />
                <span>{t('popularGames') || 'Popular Games'}</span>
              </h2>
            </div>

            <Suspense fallback={<GameListSkeleton />}>
              {popularLoading ? (
                <GameListSkeleton />
              ) : (
                <VirtualizedGameList
                  games={popularGames}
                  locale={locale}
                  enableVirtualization={false}
                  className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                />
              )}
            </Suspense>
          </motion.div>
        </div>
      </section>

      {/* 新游戏区域 */}
      <section className="py-12 bg-secondary/20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-2xl md:text-3xl font-bold flex items-center space-x-2">
                <Clock className="h-6 w-6 text-primary" />
                <span>{t('newGames') || 'New Games'}</span>
              </h2>
            </div>

            <Suspense fallback={<GameListSkeleton />}>
              {newLoading ? (
                <GameListSkeleton />
              ) : (
                <VirtualizedGameList
                  games={newGames}
                  locale={locale}
                  enableVirtualization={false}
                  className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                />
              )}
            </Suspense>
          </motion.div>
        </div>
      </section>

      {/* 分类游戏区域 */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-2xl md:text-3xl font-bold">
                {displayCategories.find(c => c.id === selectedCategory)?.name || 'Games'}
              </h2>
              <div className="text-sm text-muted-foreground">
                {categoryGames.length} games
              </div>
            </div>

            <Suspense fallback={<GameListSkeleton />}>
              <InfiniteScrollGameList
                games={categoryGames}
                loading={categoryLoading}
                hasMore={hasMore}
                onLoadMore={loadMore}
                locale={locale}
                enableVirtualization={categoryGames.length > 50}
                className="min-h-[600px]"
              />
            </Suspense>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

// 骨架屏组件
function GameListSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {Array.from({ length: 8 }).map((_, i) => (
        <div key={i} className="space-y-3">
          <div className="aspect-video bg-secondary animate-pulse rounded-lg"></div>
          <div className="space-y-2">
            <div className="h-4 bg-secondary animate-pulse rounded w-3/4"></div>
            <div className="h-3 bg-secondary animate-pulse rounded w-1/2"></div>
          </div>
        </div>
      ))}
    </div>
  );
}
