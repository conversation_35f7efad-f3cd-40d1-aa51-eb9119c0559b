'use client';

import React, { useState, useMemo } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Grid, List, Star, Users } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { gamesData, GameCardProps } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import GameCard from '@/components/GameCard';
import Breadcrumb, { breadcrumbPresets, BreadcrumbStructuredData } from '@/components/Breadcrumb';
import { useMobileDetection } from '@/components/MobileDetection';
import MobileNavigation from '@/components/MobileNavigation';
import ResponsiveGrid, { ResponsiveContainer } from '@/components/ResponsiveGrid';

// 游戏分类定义
const gameCategories = {
  action: {
    name: { en: 'Action Games', zh: '动作游戏' },
    description: { 
      en: 'Fast-paced games with exciting combat, adventure, and thrilling gameplay',
      zh: '快节奏的游戏，包含刺激的战斗、冒险和惊险的游戏玩法'
    },
    icon: '⚔️',
    color: 'from-red-500 to-orange-500'
  },
  adventure: {
    name: { en: 'Adventure Games', zh: '冒险游戏' },
    description: { 
      en: 'Explore new worlds, solve puzzles, and embark on epic journeys',
      zh: '探索新世界，解决谜题，踏上史诗般的旅程'
    },
    icon: '🗺️',
    color: 'from-green-500 to-emerald-500'
  },
  racing: {
    name: { en: 'Racing Games', zh: '竞速游戏' },
    description: { 
      en: 'High-speed racing games with cars, motorcycles, and more',
      zh: '高速竞速游戏，包含汽车、摩托车等载具'
    },
    icon: '🏎️',
    color: 'from-blue-500 to-cyan-500'
  },
  shooting: {
    name: { en: 'Shooting Games', zh: '射击游戏' },
    description: { 
      en: 'Action-packed shooting games with various weapons and targets',
      zh: '充满动作的射击游戏，拥有各种武器和目标'
    },
    icon: '🎯',
    color: 'from-purple-500 to-pink-500'
  },
  puzzle: {
    name: { en: 'Puzzle Games', zh: '益智游戏' },
    description: { 
      en: 'Brain-teasing puzzles and logic games to challenge your mind',
      zh: '挑战大脑的谜题和逻辑游戏'
    },
    icon: '🧩',
    color: 'from-yellow-500 to-amber-500'
  },
  strategy: {
    name: { en: 'Strategy Games', zh: '策略游戏' },
    description: { 
      en: 'Plan, build, and conquer in these strategic gameplay experiences',
      zh: '在这些策略游戏体验中规划、建设和征服'
    },
    icon: '♟️',
    color: 'from-indigo-500 to-purple-500'
  },
  sports: {
    name: { en: 'Sports Games', zh: '体育游戏' },
    description: { 
      en: 'Play your favorite sports virtually with realistic gameplay',
      zh: '通过逼真的游戏玩法虚拟体验您喜爱的体育运动'
    },
    icon: '⚽',
    color: 'from-teal-500 to-green-500'
  },
  simulation: {
    name: { en: 'Simulation Games', zh: '模拟游戏' },
    description: { 
      en: 'Realistic simulations of real-world activities and scenarios',
      zh: '真实世界活动和场景的逼真模拟'
    },
    icon: '🏗️',
    color: 'from-gray-500 to-slate-500'
  }
};

export default function CategoryPage() {
  const params = useParams();
  const locale = useLocale();
  const t = useTranslations('Category');
  const { isMobile, isTablet } = useMobileDetection();
  
  const categoryId = params.id as string;
  const [sortBy, setSortBy] = useState<'rating' | 'name' | 'newest'>('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 获取分类信息
  const categoryInfo = gameCategories[categoryId as keyof typeof gameCategories];
  
  // 获取该分类的游戏
  const categoryGames = useMemo(() => {
    const games = gamesData[categoryId] || [];
    
    // 添加游戏详情信息
    const gamesWithDetails = games.map(game => {
      const gameDetail = gameDetailsData[game.id];
      return {
        ...game,
        title: gameDetail?.title || game.title,
        category: gameDetail?.category || game.category,
      };
    });

    // 排序
    return gamesWithDetails.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          const nameA = a.title[locale as keyof typeof a.title] || a.title['en'];
          const nameB = b.title[locale as keyof typeof b.title] || b.title['en'];
          return nameA.localeCompare(nameB);
        case 'newest':
          // 这里可以根据游戏的添加时间排序，暂时使用ID排序
          return b.id.localeCompare(a.id);
        case 'rating':
        default:
          return b.rating - a.rating;
      }
    });
  }, [categoryId, sortBy, locale]);

  // 如果分类不存在，显示404
  if (!categoryInfo) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Category Not Found</h1>
          <p className="text-muted-foreground mb-6">
            The category "{categoryId}" does not exist.
          </p>
          <Link
            href={`/${locale}/games`}
            className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            Browse All Games
          </Link>
        </div>
      </div>
    );
  }

  const categoryName = categoryInfo.name[locale as keyof typeof categoryInfo.name] || categoryInfo.name['en'];
  const categoryDescription = categoryInfo.description[locale as keyof typeof categoryInfo.description] || categoryInfo.description['en'];

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <ResponsiveContainer padding="md" className="py-4">
        <Breadcrumb items={breadcrumbPresets.gameCategory(categoryName, locale)} />
        <BreadcrumbStructuredData items={breadcrumbPresets.gameCategory(categoryName, locale)} />
      </ResponsiveContainer>

      {/* 分类头部 */}
      <div className={`bg-gradient-to-r ${categoryInfo.color} py-16`}>
        <ResponsiveContainer>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center text-white"
          >
            <div className="text-6xl mb-4">{categoryInfo.icon}</div>
            <h1 className={`${
              isMobile ? 'text-3xl' : 'text-4xl md:text-5xl'
            } font-bold mb-4`}>
              {categoryName}
            </h1>
            <p className={`${
              isMobile ? 'text-base px-4' : 'text-lg md:text-xl'
            } opacity-90 max-w-3xl mx-auto mb-6`}>
              {categoryDescription}
            </p>
            
            {/* 统计信息 */}
            <div className="flex items-center justify-center space-x-8">
              <div className="text-center">
                <div className="text-2xl font-bold">{categoryGames.length}</div>
                <div className="text-sm opacity-80">{t('games') || 'Games'}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {(categoryGames.reduce((sum, game) => sum + game.rating, 0) / categoryGames.length).toFixed(1)}
                </div>
                <div className="text-sm opacity-80">{t('avgRating') || 'Avg Rating'}</div>
              </div>
            </div>
          </motion.div>
        </ResponsiveContainer>
      </div>

      {/* 过滤器和视图控制 */}
      <ResponsiveContainer className="py-6">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">
              {t('showing')} {categoryGames.length} {t('games')}
            </span>
          </div>

          <div className="flex items-center space-x-4">
            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'rating' | 'name' | 'newest')}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="rating">{t('sortRating') || 'Rating'}</option>
              <option value="name">{t('sortName') || 'Name'}</option>
              <option value="newest">{t('sortNewest') || 'Newest'}</option>
            </select>

            {/* 视图模式切换 */}
            {!isMobile && (
              <div className="flex items-center border border-border rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-l-lg`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-r-lg`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 游戏列表 */}
        {categoryGames.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {viewMode === 'grid' || isMobile ? (
              <ResponsiveGrid
                mobileColumns={1}
                tabletColumns={2}
                desktopColumns={4}
                className="gap-6"
              >
                {categoryGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <GameCard game={game} locale={locale} priority={index < 8} />
                  </motion.div>
                ))}
              </ResponsiveGrid>
            ) : (
              <div className="space-y-4">
                {categoryGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="flex items-center space-x-4 p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                  >
                    <div className="relative w-24 h-16 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                      <img
                        src={game.image}
                        alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-foreground line-clamp-1 mb-1">
                        {game.title[locale as keyof typeof game.title] || game.title['en']}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Star className="h-3 w-3 mr-1" />
                          {game.rating}
                        </span>
                        <span className="flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          Single
                        </span>
                      </div>
                    </div>
                    <Link
                      href={`/${locale}/game/${game.id}`}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      {t('play') || 'Play'}
                    </Link>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🎮</div>
            <h3 className="text-xl font-semibold mb-2">
              {t('noGames') || 'No games found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('noGamesDescription') || 'No games are available in this category yet'}
            </p>
            <Link
              href={`/${locale}/games`}
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('browseAll') || 'Browse All Games'}
            </Link>
          </motion.div>
        )}
      </ResponsiveContainer>

      {/* 移动端导航 */}
      {(isMobile || isTablet) && <MobileNavigation />}
    </div>
  );
}
