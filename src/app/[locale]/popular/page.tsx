'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Star, Users, Grid, List } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { gamesData, GameCardProps } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import GameCard from '@/components/GameCard';
import Breadcrumb from '@/components/Breadcrumb';
import { useMobileDetection } from '@/components/MobileDetection';
import MobileNavigation from '@/components/MobileNavigation';
import ResponsiveGrid, { ResponsiveContainer, ResponsiveText } from '@/components/ResponsiveGrid';

export default function PopularGamesPage() {
  const locale = useLocale();
  const t = useTranslations('Popular');
  const { isMobile, isTablet } = useMobileDetection();
  
  const [sortBy, setSortBy] = useState<'rating' | 'plays' | 'trending'>('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [timeFilter, setTimeFilter] = useState<'all' | 'week' | 'month' | 'year'>('all');

  // 获取热门游戏（基于评分和标签）
  const popularGames = useMemo(() => {
    type PopularGame = GameCardProps & { popularityScore: number; categoryId: string };
    const games: PopularGame[] = [];
    
    Object.entries(gamesData).forEach(([category, categoryGames]) => {
      categoryGames.forEach(game => {
        const gameDetail = gameDetailsData[game.id];
        if (gameDetail) {
          // 计算热门度分数
          let popularityScore = game.rating * 10; // 基础评分
          
          // 如果有热门标签，增加分数
          if (game.tags?.includes('popular')) popularityScore += 50;
          if (game.tags?.includes('trending')) popularityScore += 30;
          if (game.tags?.includes('featured')) popularityScore += 20;
          
          // 根据评分加权
          if (game.rating >= 4.5) popularityScore += 20;
          else if (game.rating >= 4.0) popularityScore += 10;
          
          games.push({
            ...game,
            title: gameDetail.title,
            category: gameDetail.category,
            categoryId: category,
            popularityScore
          });
        }
      });
    });

    // 只返回热门度分数较高的游戏
    const filtered = games.filter(game => game.popularityScore >= 40);

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'plays':
          // 模拟游戏次数（实际应该从数据库获取）
          return b.popularityScore - a.popularityScore;
        case 'trending':
          // 趋势游戏优先
          const aTrending = a.tags?.includes('trending') ? 1 : 0;
          const bTrending = b.tags?.includes('trending') ? 1 : 0;
          if (aTrending !== bTrending) return bTrending - aTrending;
          return b.popularityScore - a.popularityScore;
        case 'rating':
        default:
          return b.rating - a.rating;
      }
    });

    return filtered;
  }, [sortBy]);

  // 获取排行榜数据
  const topGames = useMemo(() => {
    return popularGames.slice(0, 10).map((game, index) => ({
      ...game,
      rank: index + 1
    }));
  }, [popularGames]);

  // 统计信息
  const stats = useMemo(() => {
    const totalPlays = popularGames.reduce((sum, game) => sum + (game.popularityScore * 100), 0);
    const avgRating = popularGames.reduce((sum, game) => sum + game.rating, 0) / popularGames.length;
    
    return {
      totalGames: popularGames.length,
      totalPlays: Math.floor(totalPlays),
      avgRating: avgRating.toFixed(1),
      trendingCount: popularGames.filter(game => game.tags?.includes('trending')).length
    };
  }, [popularGames]);

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <ResponsiveContainer padding="md" className="py-4">
        <Breadcrumb items={[{ label: t('popularGames') || 'Popular Games', isCurrentPage: true }]} />
      </ResponsiveContainer>

      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-orange-500 to-red-500 py-16">
        <ResponsiveContainer>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center text-white"
          >
            <div className="flex items-center justify-center mb-4">
              <TrendingUp className="h-12 w-12 mr-3" />
              <ResponsiveText
                as="h1"
                size="4xl"
                className="font-bold"
              >
                {t('title') || 'Popular Games'}
              </ResponsiveText>
            </div>
            <ResponsiveText
              size="lg"
              className="opacity-90 max-w-3xl mx-auto mb-8"
            >
              {t('description') || 'Discover the most popular and trending games played by millions of users worldwide'}
            </ResponsiveText>
            
            {/* 统计信息 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalGames}</div>
                <div className="text-sm opacity-80">{t('popularGames') || 'Popular Games'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{(stats.totalPlays / 1000000).toFixed(1)}M</div>
                <div className="text-sm opacity-80">{t('totalPlays') || 'Total Plays'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.avgRating}</div>
                <div className="text-sm opacity-80">{t('avgRating') || 'Avg Rating'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.trendingCount}</div>
                <div className="text-sm opacity-80">{t('trending') || 'Trending'}</div>
              </div>
            </div>
          </motion.div>
        </ResponsiveContainer>
      </div>

      {/* 热门排行榜 */}
      <ResponsiveContainer className="py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold mb-6 text-center">
            {t('topGames') || 'Top 10 Games'}
          </h2>
          <div className="bg-secondary/30 rounded-lg p-6">
            <div className="grid gap-4">
              {topGames.slice(0, 5).map((game) => (
                <Link
                  key={game.id}
                  href={`/${locale}/game/${game.id}`}
                  className="flex items-center space-x-4 p-4 rounded-lg hover:bg-secondary transition-colors group"
                >
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full font-bold text-white ${
                    game.rank === 1 ? 'bg-yellow-500' :
                    game.rank === 2 ? 'bg-gray-400' :
                    game.rank === 3 ? 'bg-amber-600' :
                    'bg-primary'
                  }`}>
                    {game.rank}
                  </div>
                  <div className="relative w-16 h-12 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                    <img
                      src={game.image}
                      alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-foreground line-clamp-1 group-hover:text-primary transition-colors">
                      {game.title[locale as keyof typeof game.title] || game.title['en']}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        {game.rating}
                      </span>
                      <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                      {game.tags?.includes('trending') && (
                        <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs">
                          🔥 {t('trending') || 'Trending'}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{(game.popularityScore * 100).toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">{t('plays') || 'plays'}</div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </motion.div>
      </ResponsiveContainer>

      {/* 过滤器和视图控制 */}
      <ResponsiveContainer className="py-6">
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">
              {t('showing')} {popularGames.length} {t('popularGames')}
            </span>
          </div>

          <div className="flex items-center space-x-4">
            {/* 时间过滤器 */}
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value as 'all' | 'week' | 'month' | 'year')}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="all">{t('allTime') || 'All Time'}</option>
              <option value="week">{t('thisWeek') || 'This Week'}</option>
              <option value="month">{t('thisMonth') || 'This Month'}</option>
              <option value="year">{t('thisYear') || 'This Year'}</option>
            </select>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'rating' | 'plays' | 'trending')}
              className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="rating">{t('sortRating') || 'Rating'}</option>
              <option value="plays">{t('sortPlays') || 'Most Played'}</option>
              <option value="trending">{t('sortTrending') || 'Trending'}</option>
            </select>

            {/* 视图模式切换 */}
            {!isMobile && (
              <div className="flex items-center border border-border rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-l-lg`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-r-lg`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 游戏列表 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {viewMode === 'grid' || isMobile ? (
            <ResponsiveGrid
              mobileColumns={1}
              tabletColumns={2}
              desktopColumns={4}
              className="gap-6"
            >
              {popularGames.map((game, index) => (
                <motion.div
                  key={game.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.02 }}
                  className="relative"
                >
                  {/* 热门标识 */}
                  {game.tags?.includes('trending') && (
                    <div className="absolute top-2 right-2 z-10 px-2 py-1 bg-orange-500 text-white rounded-full text-xs font-medium">
                      🔥 {t('hot') || 'HOT'}
                    </div>
                  )}
                  <GameCard game={game} locale={locale} priority={index < 8} />
                </motion.div>
              ))}
            </ResponsiveGrid>
          ) : (
            <div className="space-y-4">
              {popularGames.map((game, index) => (
                <motion.div
                  key={game.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.02 }}
                  className="flex items-center space-x-4 p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                >
                  <div className="relative w-24 h-16 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                    <img
                      src={game.image}
                      alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                      className="w-full h-full object-cover"
                    />
                    {game.tags?.includes('trending') && (
                      <div className="absolute top-1 right-1 text-xs">🔥</div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-foreground line-clamp-1 mb-1">
                      {game.title[locale as keyof typeof game.title] || game.title['en']}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span className="flex items-center">
                        <Star className="h-3 w-3 mr-1" />
                        {game.rating}
                      </span>
                      <span className="flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {(game.popularityScore * 100).toLocaleString()}
                      </span>
                      <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                    </div>
                  </div>
                  <Link
                    href={`/${locale}/game/${game.id}`}
                    className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    {t('play') || 'Play'}
                  </Link>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </ResponsiveContainer>

      {/* 移动端导航 */}
      {(isMobile || isTablet) && <MobileNavigation />}
    </div>
  );
}
