'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Search, Filter, X, ArrowLeft } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { gamesData, GameCardProps, getGamesByTag } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import OptimizedImage, { imageSizes } from '@/components/OptimizedImage';
import Breadcrumb, { breadcrumbPresets, BreadcrumbStructuredData } from '@/components/Breadcrumb';

// 游戏分类数据
const categories = [
  { id: 'all', name: { en: 'All Games', zh: '所有游戏' } },
  { id: 'action', name: { en: 'Action', zh: '动作' } },
  { id: 'adventure', name: { en: 'Adventure', zh: '冒险' } },
  { id: 'racing', name: { en: 'Racing', zh: '竞速' } },
  { id: 'shooting', name: { en: 'Shooting', zh: '射击' } },
  { id: 'puzzle', name: { en: 'Puzzle', zh: '益智' } },
  { id: 'strategy', name: { en: 'Strategy', zh: '策略' } },
  { id: 'sports', name: { en: 'Sports', zh: '体育' } },
  { id: 'simulation', name: { en: 'Simulation', zh: '模拟' } }
];

interface SearchResult extends GameCardProps {
  relevanceScore: number;
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('Search');
  
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState<'relevance' | 'rating' | 'name'>('relevance');
  const [isLoading, setIsLoading] = useState(false);

  // 搜索结果计算
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) return [];

    const results: SearchResult[] = [];
    const query = searchQuery.toLowerCase();

    // 遍历所有游戏数据
    Object.entries(gamesData).forEach(([category, games]) => {
      games.forEach(game => {
        const gameDetail = gameDetailsData[game.id];
        if (gameDetail) {
          const title = gameDetail.title[locale as keyof typeof gameDetail.title] || gameDetail.title['en'];
          const categoryName = gameDetail.category[locale as keyof typeof gameDetail.category] || gameDetail.category['en'];
          const description = gameDetail.description[locale as keyof typeof gameDetail.description] || gameDetail.description['en'];
          
          // 计算相关性分数
          let relevanceScore = 0;
          
          // 标题匹配（最高权重）
          if (title.toLowerCase().includes(query)) {
            relevanceScore += title.toLowerCase().indexOf(query) === 0 ? 10 : 5;
          }
          
          // 分类匹配
          if (categoryName.toLowerCase().includes(query)) {
            relevanceScore += 3;
          }
          
          // 描述匹配
          if (description.toLowerCase().includes(query)) {
            relevanceScore += 2;
          }
          
          // 标签匹配
          if (game.tags?.some(tag => tag.toLowerCase().includes(query))) {
            relevanceScore += 4;
          }

          // 如果有匹配，添加到结果中
          if (relevanceScore > 0) {
            // 分类过滤
            if (selectedCategory === 'all' || category === selectedCategory) {
              results.push({
                ...game,
                title: gameDetail.title,
                category: gameDetail.category,
                relevanceScore
              });
            }
          }
        }
      });
    });

    // 排序
    results.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'name':
          const nameA = a.title[locale as keyof typeof a.title] || a.title['en'];
          const nameB = b.title[locale as keyof typeof b.title] || b.title['en'];
          return nameA.localeCompare(nameB);
        case 'relevance':
        default:
          return b.relevanceScore - a.relevanceScore;
      }
    });

    return results;
  }, [searchQuery, selectedCategory, sortBy, locale]);

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const params = new URLSearchParams();
      params.set('q', query.trim());
      router.replace(`/${locale}/search?${params.toString()}`);
    }
  };

  // 清除搜索
  const clearSearch = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    router.replace(`/${locale}/search`);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <div className="container mx-auto px-4 py-4">
        <Breadcrumb
          items={searchQuery ? breadcrumbPresets.searchResults(searchQuery, locale) : [
            { label: t('search') || 'Search', isCurrentPage: true }
          ]}
        />
      </div>

      {/* 搜索头部 */}
      <div className="bg-secondary/30 py-8">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-2xl mx-auto text-center"
          >
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              {t('title') || 'Search Games'}
            </h1>
            <p className="text-muted-foreground mb-6">
              {t('description') || 'Find your favorite games from our collection of 4,500+ free games'}
            </p>

            {/* 搜索栏 */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder={t('placeholder') || 'Search for games...'}
                className="w-full pl-12 pr-12 py-4 rounded-full border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all text-lg"
              />
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* 过滤器和结果 */}
      <div className="container mx-auto px-4 py-8">
        {searchQuery && (
          <>
            {/* 过滤器栏 */}
            <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium">
                  {t('foundResults', { count: searchResults.length }) || `Found ${searchResults.length} results`}
                </span>
              </div>

              <div className="flex items-center space-x-4">
                {/* 分类过滤 */}
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
                >
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name[locale as keyof typeof category.name] || category.name['en']}
                    </option>
                  ))}
                </select>

                {/* 排序 */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'relevance' | 'rating' | 'name')}
                  className="px-3 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
                >
                  <option value="relevance">{t('sortRelevance') || 'Relevance'}</option>
                  <option value="rating">{t('sortRating') || 'Rating'}</option>
                  <option value="name">{t('sortName') || 'Name'}</option>
                </select>
              </div>
            </div>

            {/* 搜索结果 */}
            {searchResults.length > 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
              >
                {searchResults.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <Link
                      href={`/${locale}/game/${game.id}`}
                      className="block group"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <div className="relative aspect-video rounded-lg overflow-hidden bg-secondary">
                        <OptimizedImage
                          src={game.image}
                          alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                          className="w-full h-full transition-transform duration-300 group-hover:scale-105"
                          width={320}
                          height={180}
                          fill={true}
                          sizes={imageSizes.gameCard}
                          placeholder="blur"
                          loading="lazy"
                        />
                      </div>
                      <div className="mt-2 space-y-1">
                        <h3 className="font-medium text-foreground line-clamp-1">
                          {game.title[locale as keyof typeof game.title] || game.title['en']}
                        </h3>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                          <span className="flex items-center">
                            ⭐ {game.rating}
                          </span>
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="text-center py-12"
              >
                <div className="text-6xl mb-4">🎮</div>
                <h3 className="text-xl font-semibold mb-2">
                  {t('noResults') || 'No games found'}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {t('noResultsDescription') || 'Try adjusting your search terms or browse our categories'}
                </p>
                <Button onClick={clearSearch} variant="outline">
                  {t('clearSearch') || 'Clear Search'}
                </Button>
              </motion.div>
            )}
          </>
        )}

        {/* 空状态 - 没有搜索查询时 */}
        {!searchQuery && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🔍</div>
            <h2 className="text-2xl font-semibold mb-2">
              {t('startSearching') || 'Start searching for games'}
            </h2>
            <p className="text-muted-foreground">
              {t('searchHint') || 'Enter a game name, category, or keyword to find your favorite games'}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}
