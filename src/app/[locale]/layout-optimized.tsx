import React from 'react';
import { NextIntlClientProvider } from 'next-intl';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';

// 导入优化组件
import GameDataProvider, { GameDataPreloader } from '@/components/GameDataProvider';
import { ThemeProvider } from 'next-themes';

// 导入样式
import '@/app/globals.css';

// 支持的语言
const locales = ['en', 'zh'];

// 生成元数据
export async function generateMetadata({
  params: { locale }
}: {
  params: { locale: string }
}): Promise<Metadata> {
  return {
    title: {
      template: '%s | FreeHubGames',
      default: 'FreeHubGames - Play Free Online Games',
    },
    description: 'Discover and play thousands of free online games instantly in your browser. Action, adventure, puzzle, racing games and more!',
    keywords: 'free games, online games, browser games, action games, puzzle games, racing games',
    authors: [{ name: 'FreeHubGames Team' }],
    creator: 'FreeHubGames',
    publisher: 'FreeHubGames',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://freehubgames.com'),
    alternates: {
      canonical: `/${locale}`,
      languages: {
        'en': '/en',
        'zh': '/zh',
      },
    },
    openGraph: {
      type: 'website',
      locale: locale,
      url: `https://freehubgames.com/${locale}`,
      title: 'FreeHubGames - Play Free Online Games',
      description: 'Discover and play thousands of free online games instantly in your browser.',
      siteName: 'FreeHubGames',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'FreeHubGames - Free Online Games',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: 'FreeHubGames - Play Free Online Games',
      description: 'Discover and play thousands of free online games instantly in your browser.',
      images: ['/og-image.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
  };
}

// 生成静态参数
export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

// 根布局组件
export default async function OptimizedRootLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // 验证语言参数
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // 加载消息
  let messages;
  try {
    messages = (await import(`../../../messages/${locale}.json`)).default;
  } catch (error) {
    notFound();
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        {/* 预连接到外部域名 */}
        <link rel="preconnect" href="https://www.onlinegames.io" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* DNS预取 */}
        <link rel="dns-prefetch" href="https://www.onlinegames.io" />
        <link rel="dns-prefetch" href="https://images.unsplash.com" />
        
        {/* 预加载关键资源 */}
        <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        
        {/* 性能提示 */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#000000" />
        <meta name="color-scheme" content="light dark" />
        
        {/* 预加载关键CSS */}
        <link rel="preload" href="/globals.css" as="style" />
        
        {/* PWA相关 */}
        <link rel="manifest" href="/manifest.json" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "FreeHubGames",
              "url": "https://freehubgames.com",
              "description": "Play thousands of free online games instantly in your browser",
              "potentialAction": {
                "@type": "SearchAction",
                "target": {
                  "@type": "EntryPoint",
                  "urlTemplate": "https://freehubgames.com/search?q={search_term_string}"
                },
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        {/* 主题提供者 */}
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {/* 国际化提供者 */}
          <NextIntlClientProvider locale={locale} messages={messages}>
            {/* 游戏数据提供者 */}
            <GameDataProvider
              autoInitialize={true}
              enablePerformanceMonitoring={process.env.NODE_ENV === 'development'}
            >
              {/* 数据预加载器 */}
              <GameDataPreloader
                showProgress={true}
                fallback={<OptimizedLoadingScreen />}
              >
                {/* 主要内容 */}
                <div className="relative flex min-h-screen flex-col">
                  <main className="flex-1">
                    {children}
                  </main>
                </div>
              </GameDataPreloader>
            </GameDataProvider>
          </NextIntlClientProvider>
        </ThemeProvider>

        {/* 性能监控脚本 */}
        {process.env.NODE_ENV === 'development' && (
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // 监控Core Web Vitals
                function sendToAnalytics(metric) {
                  console.log('Core Web Vital:', metric);
                }
                
                // 监控LCP
                new PerformanceObserver((entryList) => {
                  for (const entry of entryList.getEntries()) {
                    sendToAnalytics({
                      name: 'LCP',
                      value: entry.startTime,
                      id: entry.id,
                    });
                  }
                }).observe({entryTypes: ['largest-contentful-paint']});
                
                // 监控FID
                new PerformanceObserver((entryList) => {
                  for (const entry of entryList.getEntries()) {
                    sendToAnalytics({
                      name: 'FID',
                      value: entry.processingStart - entry.startTime,
                      id: entry.id,
                    });
                  }
                }).observe({entryTypes: ['first-input']});
                
                // 监控CLS
                let clsValue = 0;
                new PerformanceObserver((entryList) => {
                  for (const entry of entryList.getEntries()) {
                    if (!entry.hadRecentInput) {
                      clsValue += entry.value;
                      sendToAnalytics({
                        name: 'CLS',
                        value: clsValue,
                        id: entry.id,
                      });
                    }
                  }
                }).observe({entryTypes: ['layout-shift']});
              `
            }}
          />
        )}
      </body>
    </html>
  );
}

// 优化的加载屏幕
function OptimizedLoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-secondary/10">
      <div className="text-center space-y-6 max-w-md mx-auto px-4">
        {/* Logo动画 */}
        <div className="relative">
          <div className="w-16 h-16 mx-auto bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
          <div className="absolute inset-0 w-16 h-16 mx-auto border-4 border-primary/30 rounded-full animate-spin border-t-primary"></div>
        </div>
        
        {/* 标题 */}
        <div className="space-y-2">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            FreeHubGames
          </h1>
          <p className="text-muted-foreground">Loading amazing games...</p>
        </div>
        
        {/* 进度指示器 */}
        <div className="space-y-3">
          <div className="w-full bg-secondary rounded-full h-2 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
          </div>
          <div className="text-xs text-muted-foreground">
            Initializing game database and search indices
          </div>
        </div>
        
        {/* 特性列表 */}
        <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>Smart Search</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>Fast Loading</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
            <span>Optimized Images</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            <span>Instant Play</span>
          </div>
        </div>
      </div>
    </div>
  );
}
