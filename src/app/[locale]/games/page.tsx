'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Grid, List, Star, ChevronRight } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { gamesData, GameCardProps } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import GameCard from '@/components/GameCard';
import Breadcrumb from '@/components/Breadcrumb';
import { useMobileDetection } from '@/components/MobileDetection';
import MobileNavigation from '@/components/MobileNavigation';
import ResponsiveGrid, { ResponsiveContainer, ResponsiveText } from '@/components/ResponsiveGrid';

// 游戏分类定义
const gameCategories = [
  { id: 'action', name: { en: 'Action', zh: '动作' }, icon: '⚔️', color: 'from-red-500 to-orange-500' },
  { id: 'adventure', name: { en: 'Adventure', zh: '冒险' }, icon: '🗺️', color: 'from-green-500 to-emerald-500' },
  { id: 'racing', name: { en: 'Racing', zh: '竞速' }, icon: '🏎️', color: 'from-blue-500 to-cyan-500' },
  { id: 'shooting', name: { en: 'Shooting', zh: '射击' }, icon: '🎯', color: 'from-purple-500 to-pink-500' },
  { id: 'puzzle', name: { en: 'Puzzle', zh: '益智' }, icon: '🧩', color: 'from-yellow-500 to-amber-500' },
  { id: 'strategy', name: { en: 'Strategy', zh: '策略' }, icon: '♟️', color: 'from-indigo-500 to-purple-500' },
  { id: 'sports', name: { en: 'Sports', zh: '体育' }, icon: '⚽', color: 'from-teal-500 to-green-500' },
  { id: 'simulation', name: { en: 'Simulation', zh: '模拟' }, icon: '🏗️', color: 'from-gray-500 to-slate-500' },
];

export default function GamesPage() {
  const locale = useLocale();
  const t = useTranslations('Games');
  const { isMobile, isTablet } = useMobileDetection();
  
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'rating' | 'name' | 'newest' | 'popular'>('rating');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');

  // 获取所有游戏
  const allGames = useMemo(() => {
    const games: (GameCardProps & { categoryId: string })[] = [];

    Object.entries(gamesData).forEach(([category, categoryGames]) => {
      categoryGames.forEach(game => {
        const gameDetail = gameDetailsData[game.id];
        games.push({
          ...game,
          title: gameDetail?.title || game.title,
          category: gameDetail?.category || game.category,
          categoryId: category,
        });
      });
    });

    return games;
  }, []);

  // 过滤和排序游戏
  const filteredGames = useMemo(() => {
    let filtered = [...allGames];

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(game => game.categoryId === selectedCategory);
    }

    // 按搜索词过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(game => {
        const title = game.title[locale as keyof typeof game.title] || game.title['en'];
        const category = game.category[locale as keyof typeof game.category] || game.category['en'];
        return title.toLowerCase().includes(query) || 
               category.toLowerCase().includes(query) ||
               game.tags?.some(tag => tag.toLowerCase().includes(query));
      });
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          const nameA = a.title[locale as keyof typeof a.title] || a.title['en'];
          const nameB = b.title[locale as keyof typeof b.title] || b.title['en'];
          return nameA.localeCompare(nameB);
        case 'newest':
          return b.id.localeCompare(a.id);
        case 'popular':
          // 这里可以根据实际的流行度数据排序，暂时使用评分
          return b.rating - a.rating;
        case 'rating':
        default:
          return b.rating - a.rating;
      }
    });

    return filtered;
  }, [allGames, selectedCategory, searchQuery, sortBy, locale]);

  // 计算统计信息
  const stats = useMemo(() => {
    const totalGames = allGames.length;
    const avgRating = allGames.reduce((sum, game) => sum + game.rating, 0) / totalGames;
    const categoryCounts = gameCategories.map(category => ({
      ...category,
      count: allGames.filter(game => game.categoryId === category.id).length
    }));

    return {
      totalGames,
      avgRating: avgRating.toFixed(1),
      categoryCounts
    };
  }, [allGames]);

  return (
    <div className="min-h-screen bg-background">
      {/* 面包屑导航 */}
      <ResponsiveContainer padding="md" className="py-4">
        <Breadcrumb items={[{ label: t('allGames') || 'All Games', isCurrentPage: true }]} />
      </ResponsiveContainer>

      {/* 页面头部 */}
      <div className="bg-gradient-to-r from-primary to-primary/70 py-16">
        <ResponsiveContainer>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center text-white"
          >
            <div className="text-6xl mb-4">🎮</div>
            <ResponsiveText
              as="h1"
              size="4xl"
              className="font-bold mb-4"
            >
              {t('title') || 'All Games'}
            </ResponsiveText>
            <ResponsiveText
              size="lg"
              className="opacity-90 max-w-3xl mx-auto mb-8"
            >
              {t('description') || 'Discover and play from our collection of 4,500+ free online games'}
            </ResponsiveText>
            
            {/* 统计信息 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.totalGames}</div>
                <div className="text-sm opacity-80">{t('totalGames') || 'Total Games'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{gameCategories.length}</div>
                <div className="text-sm opacity-80">{t('categories') || 'Categories'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">{stats.avgRating}</div>
                <div className="text-sm opacity-80">{t('avgRating') || 'Avg Rating'}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">100%</div>
                <div className="text-sm opacity-80">{t('free') || 'Free'}</div>
              </div>
            </div>
          </motion.div>
        </ResponsiveContainer>
      </div>

      {/* 游戏分类卡片 */}
      <ResponsiveContainer className="py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h2 className="text-2xl font-bold mb-6 text-center">
            {t('browseByCategory') || 'Browse by Category'}
          </h2>
          <ResponsiveGrid
            mobileColumns={2}
            tabletColumns={3}
            desktopColumns={4}
            className="gap-4 mb-12"
          >
            {stats.categoryCounts.map((category) => (
              <Link
                key={category.id}
                href={`/${locale}/category/${category.id}`}
                className="group"
              >
                <div className={`relative p-6 rounded-xl bg-gradient-to-br ${category.color} text-white overflow-hidden transition-transform duration-300 group-hover:scale-105`}>
                  <div className="relative z-10">
                    <div className="text-3xl mb-2">{category.icon}</div>
                    <h3 className="font-semibold mb-1">
                      {category.name[locale as keyof typeof category.name] || category.name['en']}
                    </h3>
                    <p className="text-sm opacity-90">
                      {category.count} {t('games') || 'games'}
                    </p>
                  </div>
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </Link>
            ))}
          </ResponsiveGrid>
        </motion.div>
      </ResponsiveContainer>

      {/* 搜索和过滤器 */}
      <ResponsiveContainer className="py-6">
        <div className="bg-secondary/30 rounded-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            {/* 搜索栏 */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={t('searchPlaceholder') || 'Search games...'}
                className="w-full pl-10 pr-4 py-3 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
              />
            </div>

            {/* 分类过滤器 */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-3 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="all">{t('allCategories') || 'All Categories'}</option>
              {gameCategories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name[locale as keyof typeof category.name] || category.name['en']}
                </option>
              ))}
            </select>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'rating' | 'name' | 'newest' | 'popular')}
              className="px-4 py-3 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50"
            >
              <option value="rating">{t('sortRating') || 'Rating'}</option>
              <option value="popular">{t('sortPopular') || 'Popular'}</option>
              <option value="newest">{t('sortNewest') || 'Newest'}</option>
              <option value="name">{t('sortName') || 'Name'}</option>
            </select>

            {/* 视图模式切换 */}
            {!isMobile && (
              <div className="flex items-center border border-border rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-3 ${viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-l-lg`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-3 ${viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-secondary'} transition-colors rounded-r-lg`}
                >
                  <List className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>

          {/* 结果统计 */}
          <div className="text-sm text-muted-foreground">
            {t('showingResults', { count: filteredGames.length, total: allGames.length }) || 
             `Showing ${filteredGames.length} of ${allGames.length} games`}
          </div>
        </div>

        {/* 游戏列表 */}
        {filteredGames.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {viewMode === 'grid' || isMobile ? (
              <ResponsiveGrid
                mobileColumns={1}
                tabletColumns={2}
                desktopColumns={4}
                className="gap-6"
              >
                {filteredGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.02 }}
                  >
                    <GameCard game={game} locale={locale} priority={index < 12} />
                  </motion.div>
                ))}
              </ResponsiveGrid>
            ) : (
              <div className="space-y-4">
                {filteredGames.map((game, index) => (
                  <motion.div
                    key={game.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.02 }}
                    className="flex items-center space-x-4 p-4 rounded-lg border border-border hover:border-primary/50 transition-colors"
                  >
                    <div className="relative w-24 h-16 rounded-lg overflow-hidden bg-secondary flex-shrink-0">
                      <img
                        src={game.image}
                        alt={game.title[locale as keyof typeof game.title] || game.title['en']}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-foreground line-clamp-1 mb-1">
                        {game.title[locale as keyof typeof game.title] || game.title['en']}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Star className="h-3 w-3 mr-1" />
                          {game.rating}
                        </span>
                        <span>{game.category[locale as keyof typeof game.category] || game.category['en']}</span>
                      </div>
                    </div>
                    <Link
                      href={`/${locale}/game/${game.id}`}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      {t('play') || 'Play'}
                    </Link>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold mb-2">
              {t('noGamesFound') || 'No games found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('noGamesDescription') || 'Try adjusting your search terms or filters'}
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
              }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('clearFilters') || 'Clear Filters'}
            </button>
          </motion.div>
        )}
      </ResponsiveContainer>

      {/* 移动端导航 */}
      {(isMobile || isTablet) && <MobileNavigation />}
    </div>
  );
}
