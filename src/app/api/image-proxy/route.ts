import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const imageUrl = searchParams.get('url');
  const width = searchParams.get('w');
  const quality = searchParams.get('q') || '85';

  if (!imageUrl) {
    return new NextResponse('Missing image URL', { status: 400 });
  }

  try {
    // 验证URL是否来自允许的域名
    const allowedDomains = [
      'www.onlinegames.io',
      'onlinegames.io',
      'images.unsplash.com'
    ];
    
    const url = new URL(imageUrl);
    if (!allowedDomains.includes(url.hostname)) {
      return new NextResponse('Domain not allowed', { status: 403 });
    }

    // 获取原始图片
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ImageProxy/1.0)',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }

    const contentType = response.headers.get('content-type') || 'image/jpeg';
    const buffer = await response.arrayBuffer();

    // 设置缓存头
    const headers = new Headers({
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=31536000, immutable', // 1年缓存
      'CDN-Cache-Control': 'public, max-age=31536000',
      'Vercel-CDN-Cache-Control': 'public, max-age=31536000',
    });

    return new NextResponse(buffer, { headers });
  } catch (error) {
    console.error('Image proxy error:', error);
    
    // 返回一个占位符图片
    const placeholderSvg = `
      <svg width="320" height="180" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="Arial, sans-serif" font-size="14">
          Image not available
        </text>
      </svg>
    `;
    
    return new NextResponse(placeholderSvg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=300', // 5分钟缓存
      },
    });
  }
}
