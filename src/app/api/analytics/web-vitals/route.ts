import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate the data
    if (!data.metric || !data.value) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Log the web vital metric
    console.log('Web Vital:', {
      metric: data.metric,
      value: data.value,
      rating: data.rating,
      url: data.url,
      timestamp: new Date(data.timestamp).toISOString(),
    });

    // Here you could store the data in a database
    // await storeWebVital(data);

    // Or send to external analytics service
    // await sendToExternalAnalytics(data);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing web vitals:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Example function to store web vitals in database
async function storeWebVital(data: any) {
  // Implementation would depend on your database choice
  // Example with Prisma:
  /*
  await prisma.webVital.create({
    data: {
      metric: data.metric,
      value: data.value,
      rating: data.rating,
      url: data.url,
      userAgent: data.userAgent,
      timestamp: new Date(data.timestamp),
    },
  });
  */
}

// Example function to send to external analytics
async function sendToExternalAnalytics(data: any) {
  // Example: Send to Google Analytics Measurement Protocol
  /*
  const params = new URLSearchParams({
    v: '1',
    tid: 'GA_TRACKING_ID',
    cid: data.id,
    t: 'event',
    ec: 'Web Vitals',
    ea: data.metric,
    el: data.id,
    ev: Math.round(data.value),
  });

  await fetch('https://www.google-analytics.com/collect', {
    method: 'POST',
    body: params,
  });
  */
}
