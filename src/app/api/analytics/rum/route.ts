import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Validate the data
    if (!data.timestamp || !data.url) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Log the RUM data
    console.log('RUM Data:', {
      url: data.url,
      referrer: data.referrer,
      viewport: data.viewport,
      connection: data.connection,
      deviceMemory: data.deviceMemory,
      hardwareConcurrency: data.hardwareConcurrency,
      timestamp: new Date(data.timestamp).toISOString(),
    });

    // Here you could store the RUM data in a database
    // await storeRUMData(data);

    // Or send to external analytics service
    // await sendRUMToExternalService(data);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing RUM data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Example function to store RUM data
async function storeRUMData(data: any) {
  // Implementation would depend on your database choice
  // Example with Prisma:
  /*
  await prisma.rumData.create({
    data: {
      url: data.url,
      referrer: data.referrer,
      userAgent: data.userAgent,
      viewportWidth: data.viewport.width,
      viewportHeight: data.viewport.height,
      connectionType: data.connection?.effectiveType,
      downlink: data.connection?.downlink,
      rtt: data.connection?.rtt,
      deviceMemory: data.deviceMemory,
      hardwareConcurrency: data.hardwareConcurrency,
      timestamp: new Date(data.timestamp),
    },
  });
  */
}

// Example function to send to external service
async function sendRUMToExternalService(data: any) {
  // Example: Send to a RUM service like DataDog, New Relic, etc.
  /*
  await fetch('https://your-rum-service.com/api/data', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY',
    },
    body: JSON.stringify(data),
  });
  */
}
