'use client';

import { gamesData, GameCardProps } from '@/data/games';
import { gameDetailsData } from '@/data/game-details';
import { 
  gameDataConfig, 
  ExtendedGameData, 
  SearchIndex, 
  CategoryIndex, 
  TagIndex,
  CacheManager 
} from '@/config/gameDataConfig';

// 内存缓存实现
class MemoryCache implements CacheManager {
  private cache = new Map<string, { value: any; expiry: number }>();
  private maxSize = gameDataConfig.cache.maxEntries;

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  set<T>(key: string, value: T, ttl?: number): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    const expiry = Date.now() + (ttl || gameDataConfig.cache.ttl);
    this.cache.set(key, { value, expiry });
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// 游戏数据管理器
class GameDataManager {
  private cache: CacheManager;
  private searchIndex: SearchIndex[] = [];
  private categoryIndex: CategoryIndex[] = [];
  private tagIndex: TagIndex[] = [];
  private allGames: ExtendedGameData[] = [];
  private initialized = false;

  constructor() {
    this.cache = new MemoryCache();
  }

  // 初始化数据管理器
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 预处理游戏数据
      this.preprocessGameData();
      
      // 生成搜索索引
      if (gameDataConfig.preprocessing.generateSearchIndex) {
        this.generateSearchIndex();
      }
      
      // 生成分类索引
      if (gameDataConfig.preprocessing.generateCategoryIndex) {
        this.generateCategoryIndex();
      }
      
      // 生成标签索引
      if (gameDataConfig.preprocessing.generateTagIndex) {
        this.generateTagIndex();
      }

      this.initialized = true;
      console.log('GameDataManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize GameDataManager:', error);
      throw error;
    }
  }

  // 预处理游戏数据
  private preprocessGameData(): void {
    const games: ExtendedGameData[] = [];
    
    Object.entries(gamesData).forEach(([categoryId, categoryGames]) => {
      categoryGames.forEach(game => {
        const gameDetail = gameDetailsData[game.id];
        const extendedGame: ExtendedGameData = {
          ...game,
          categoryId,
          // 模拟额外数据
          popularity: this.calculatePopularity(game),
          addedDate: this.generateAddedDate(game.id),
          lastUpdated: new Date(),
          playCount: Math.floor(Math.random() * 10000),
          // 生成搜索索引文本
          searchIndex: this.generateSearchText(game, gameDetail),
        };
        
        games.push(extendedGame);
      });
    });

    this.allGames = games;
  }

  // 计算游戏热门度
  private calculatePopularity(game: GameCardProps): number {
    let score = game.rating * 20; // 基础评分
    
    // 标签加分
    if (game.tags?.includes('trending')) score += 30;
    if (game.tags?.includes('new')) score += 20;
    if (game.tags?.includes('popular')) score += 25;
    
    // 随机因子
    score += Math.random() * 10;
    
    return Math.round(score);
  }

  // 生成添加日期（模拟）
  private generateAddedDate(gameId: string): Date {
    const hash = gameId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    const daysAgo = Math.abs(hash) % 365;
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date;
  }

  // 生成搜索文本
  private generateSearchText(game: GameCardProps, gameDetail?: any): string {
    const texts = [
      game.title.en,
      game.title.zh,
      game.category.en,
      game.category.zh,
      ...(game.tags || []),
    ];

    if (gameDetail) {
      texts.push(
        gameDetail.description?.en || '',
        gameDetail.description?.zh || ''
      );
    }

    return texts.join(' ').toLowerCase();
  }

  // 生成搜索索引
  private generateSearchIndex(): void {
    this.searchIndex = this.allGames.map(game => ({
      id: game.id,
      title: game.title.en + ' ' + game.title.zh,
      category: game.category.en + ' ' + game.category.zh,
      tags: game.tags || [],
      searchText: game.searchIndex || '',
      weight: this.calculateSearchWeight(game),
    }));
  }

  // 计算搜索权重
  private calculateSearchWeight(game: ExtendedGameData): number {
    let weight = game.rating * 2;
    weight += (game.popularity || 0) * 0.1;
    if (game.tags?.includes('trending')) weight += 5;
    return weight;
  }

  // 生成分类索引
  private generateCategoryIndex(): void {
    const categoryMap = new Map<string, ExtendedGameData[]>();
    
    this.allGames.forEach(game => {
      const categoryId = game.categoryId || 'other';
      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, []);
      }
      categoryMap.get(categoryId)!.push(game);
    });

    this.categoryIndex = Array.from(categoryMap.entries()).map(([id, games]) => ({
      id,
      name: games[0]?.category || { en: id, zh: id },
      gameIds: games.map(g => g.id),
      gameCount: games.length,
      averageRating: games.reduce((sum, g) => sum + g.rating, 0) / games.length,
    }));
  }

  // 生成标签索引
  private generateTagIndex(): void {
    const tagMap = new Map<string, ExtendedGameData[]>();
    
    this.allGames.forEach(game => {
      game.tags?.forEach(tag => {
        if (!tagMap.has(tag)) {
          tagMap.set(tag, []);
        }
        tagMap.get(tag)!.push(game);
      });
    });

    this.tagIndex = Array.from(tagMap.entries()).map(([tag, games]) => ({
      tag,
      gameIds: games.map(g => g.id),
      gameCount: games.length,
      category: Array.from(new Set(games.map(g => g.categoryId || 'other'))),
    }));
  }

  // 获取所有游戏
  getAllGames(): ExtendedGameData[] {
    return this.allGames;
  }

  // 根据ID获取游戏
  getGameById(id: string): ExtendedGameData | null {
    const cacheKey = `game_${id}`;
    let game = this.cache.get<ExtendedGameData>(cacheKey);
    
    if (!game) {
      game = this.allGames.find(g => g.id === id) || null;
      if (game) {
        this.cache.set(cacheKey, game);
      }
    }
    
    return game;
  }

  // 根据分类获取游戏
  getGamesByCategory(categoryId: string): ExtendedGameData[] {
    const cacheKey = `category_${categoryId}`;
    let games = this.cache.get<ExtendedGameData[]>(cacheKey);
    
    if (!games) {
      games = this.allGames.filter(g => g.categoryId === categoryId);
      this.cache.set(cacheKey, games);
    }
    
    return games;
  }

  // 根据标签获取游戏
  getGamesByTag(tag: string): ExtendedGameData[] {
    const cacheKey = `tag_${tag}`;
    let games = this.cache.get<ExtendedGameData[]>(cacheKey);
    
    if (!games) {
      games = this.allGames.filter(g => g.tags?.includes(tag));
      this.cache.set(cacheKey, games);
    }
    
    return games;
  }

  // 搜索游戏
  searchGames(query: string, options: {
    limit?: number;
    category?: string;
    sortBy?: 'relevance' | 'rating' | 'popularity' | 'name' | 'date';
    locale?: string;
  } = {}): ExtendedGameData[] {
    if (!query || query.length < gameDataConfig.search.minSearchLength) {
      return [];
    }

    const { limit = gameDataConfig.search.maxResults, category, sortBy = 'relevance', locale = 'en' } = options;
    const cacheKey = `search_${query}_${category}_${sortBy}_${locale}`;
    
    let results = this.cache.get<ExtendedGameData[]>(cacheKey);
    
    if (!results) {
      results = this.performSearch(query, options);
      this.cache.set(cacheKey, results, 5 * 60 * 1000); // 5分钟缓存
    }
    
    return results.slice(0, limit);
  }

  // 执行搜索
  private performSearch(query: string, options: any): ExtendedGameData[] {
    const queryLower = query.toLowerCase();
    const results: Array<ExtendedGameData & { relevanceScore: number }> = [];

    this.allGames.forEach(game => {
      if (options.category && game.categoryId !== options.category) {
        return;
      }

      let relevanceScore = 0;
      const titleText = (game.title[options.locale] || game.title.en).toLowerCase();
      const categoryText = (game.category[options.locale] || game.category.en).toLowerCase();
      const searchText = game.searchIndex || '';

      // 标题匹配（最高权重）
      if (titleText.includes(queryLower)) {
        relevanceScore += titleText.indexOf(queryLower) === 0 ? 100 : 50;
      }

      // 分类匹配
      if (categoryText.includes(queryLower)) {
        relevanceScore += 30;
      }

      // 标签匹配
      game.tags?.forEach(tag => {
        if (tag.toLowerCase().includes(queryLower)) {
          relevanceScore += 20;
        }
      });

      // 描述匹配
      if (searchText.includes(queryLower)) {
        relevanceScore += 10;
      }

      if (relevanceScore > 0) {
        results.push({ ...game, relevanceScore });
      }
    });

    // 排序
    results.sort((a, b) => {
      switch (options.sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'name':
          const nameA = a.title[options.locale] || a.title.en;
          const nameB = b.title[options.locale] || b.title.en;
          return nameA.localeCompare(nameB);
        case 'date':
          return (b.addedDate?.getTime() || 0) - (a.addedDate?.getTime() || 0);
        case 'relevance':
        default:
          return b.relevanceScore - a.relevanceScore;
      }
    });

    return results;
  }

  // 获取热门游戏
  getPopularGames(limit: number = 20): ExtendedGameData[] {
    const cacheKey = `popular_${limit}`;
    let games = this.cache.get<ExtendedGameData[]>(cacheKey);
    
    if (!games) {
      games = [...this.allGames]
        .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
        .slice(0, limit);
      this.cache.set(cacheKey, games);
    }
    
    return games;
  }

  // 获取新游戏
  getNewGames(limit: number = 20): ExtendedGameData[] {
    const cacheKey = `new_${limit}`;
    let games = this.cache.get<ExtendedGameData[]>(cacheKey);
    
    if (!games) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      games = this.allGames
        .filter(g => g.addedDate && g.addedDate > thirtyDaysAgo)
        .sort((a, b) => (b.addedDate?.getTime() || 0) - (a.addedDate?.getTime() || 0))
        .slice(0, limit);
      this.cache.set(cacheKey, games);
    }
    
    return games;
  }

  // 获取分类索引
  getCategoryIndex(): CategoryIndex[] {
    return this.categoryIndex;
  }

  // 获取标签索引
  getTagIndex(): TagIndex[] {
    return this.tagIndex;
  }

  // 清除缓存
  clearCache(): void {
    this.cache.clear();
  }

  // 获取统计信息
  getStats() {
    return {
      totalGames: this.allGames.length,
      categories: this.categoryIndex.length,
      tags: this.tagIndex.length,
      cacheSize: this.cache.size(),
      initialized: this.initialized,
    };
  }
}

// 创建全局实例
export const gameDataManager = new GameDataManager();

// 初始化函数
export async function initializeGameData(): Promise<void> {
  if (typeof window !== 'undefined') {
    await gameDataManager.initialize();
  }
}

export default gameDataManager;
