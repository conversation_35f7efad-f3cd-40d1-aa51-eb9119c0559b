'use client';

import { useEffect, useCallback } from 'react';

interface PreloadOptions {
  priority?: boolean;
  quality?: number;
  width?: number;
  useProxy?: boolean;
}

// 图片预加载缓存
const preloadCache = new Set<string>();

// 生成代理URL
function getProxiedImageUrl(originalUrl: string, width?: number, quality?: number): string {
  if (typeof window === 'undefined') return originalUrl;
  
  const isExternalUrl = originalUrl.startsWith('http') && 
    !originalUrl.includes(window.location.hostname);
  
  if (!isExternalUrl) {
    return originalUrl;
  }

  const params = new URLSearchParams({
    url: originalUrl,
    q: String(quality || 85),
  });
  
  if (width) {
    params.set('w', String(width));
  }
  
  return `/api/image-proxy?${params.toString()}`;
}

// 单个图片预加载Hook
export function useImagePreload(
  src: string, 
  options: PreloadOptions = {}
) {
  const { priority = false, quality = 85, width, useProxy = true } = options;

  useEffect(() => {
    if (!src || preloadCache.has(src)) return;

    const processedSrc = useProxy ? getProxiedImageUrl(src, width, quality) : src;
    
    // 标记为已预加载
    preloadCache.add(src);

    if (priority) {
      // 高优先级：使用 link preload
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = processedSrc;
      document.head.appendChild(link);

      return () => {
        try {
          document.head.removeChild(link);
        } catch (e) {
          // 忽略移除错误
        }
      };
    } else {
      // 普通优先级：使用 Image 对象预加载
      const img = new Image();
      img.src = processedSrc;
      
      // 不需要清理，让浏览器缓存处理
    }
  }, [src, priority, quality, width, useProxy]);
}

// 批量图片预加载Hook
export function useBatchImagePreload(
  images: Array<{ src: string; options?: PreloadOptions }>,
  enabled: boolean = true
) {
  const preloadImages = useCallback(() => {
    if (!enabled) return;

    images.forEach(({ src, options = {} }) => {
      if (!src || preloadCache.has(src)) return;

      const { priority = false, quality = 85, width, useProxy = true } = options;
      const processedSrc = useProxy ? getProxiedImageUrl(src, width, quality) : src;
      
      preloadCache.add(src);

      if (priority) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = processedSrc;
        document.head.appendChild(link);
      } else {
        const img = new Image();
        img.src = processedSrc;
      }
    });
  }, [images, enabled]);

  useEffect(() => {
    // 延迟预加载，避免阻塞首屏渲染
    const timer = setTimeout(preloadImages, 100);
    return () => clearTimeout(timer);
  }, [preloadImages]);
}

// 智能预加载Hook - 根据用户行为预加载
export function useSmartImagePreload(
  images: string[],
  options: PreloadOptions = {}
) {
  useEffect(() => {
    if (!images.length) return;

    let timeoutId: NodeJS.Timeout;
    let isUserIdle = false;

    // 检测用户空闲状态
    const handleUserActivity = () => {
      isUserIdle = false;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        isUserIdle = true;
        startPreloading();
      }, 2000); // 2秒无活动后开始预加载
    };

    const startPreloading = () => {
      if (!isUserIdle) return;

      images.forEach((src, index) => {
        setTimeout(() => {
          if (preloadCache.has(src)) return;
          
          const { quality = 85, width, useProxy = true } = options;
          const processedSrc = useProxy ? getProxiedImageUrl(src, width, quality) : src;
          
          preloadCache.add(src);
          const img = new Image();
          img.src = processedSrc;
        }, index * 200); // 每200ms预加载一张图片
      });
    };

    // 监听用户活动
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // 初始化定时器
    handleUserActivity();

    return () => {
      clearTimeout(timeoutId);
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, [images, options]);
}

// 清理预加载缓存
export function clearPreloadCache() {
  preloadCache.clear();
}
