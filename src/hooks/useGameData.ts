'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { gameDataManager, initializeGameData } from '@/lib/gameDataManager';
import { ExtendedGameData } from '@/config/gameDataConfig';
import { gameDataConfig } from '@/config/gameDataConfig';

// 游戏数据Hook的选项
interface UseGameDataOptions {
  category?: string;
  tag?: string;
  search?: string;
  sortBy?: 'relevance' | 'rating' | 'popularity' | 'name' | 'date';
  limit?: number;
  locale?: string;
  autoLoad?: boolean;
}

// 分页选项
interface PaginationOptions {
  page: number;
  pageSize: number;
  totalPages: number;
  totalItems: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// Hook返回类型
interface UseGameDataReturn {
  games: ExtendedGameData[];
  loading: boolean;
  error: string | null;
  pagination: PaginationOptions;
  stats: any;
  
  // 操作方法
  loadMore: () => void;
  refresh: () => void;
  search: (query: string) => void;
  filterByCategory: (category: string) => void;
  filterByTag: (tag: string) => void;
  sortBy: (sortType: string) => void;
  
  // 状态
  hasMore: boolean;
  isLoadingMore: boolean;
}

// 主要的游戏数据Hook
export function useGameData(options: UseGameDataOptions = {}): UseGameDataReturn {
  const {
    category,
    tag,
    search,
    sortBy = 'popularity',
    limit = gameDataConfig.pagination.defaultPageSize,
    locale = 'en',
    autoLoad = true,
  } = options;

  const [games, setGames] = useState<ExtendedGameData[]>([]);
  const [allGames, setAllGames] = useState<ExtendedGameData[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [initialized, setInitialized] = useState(false);

  // 初始化数据管理器
  useEffect(() => {
    if (!autoLoad) return;

    const init = async () => {
      try {
        setLoading(true);
        await initializeGameData();
        setInitialized(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize game data');
      } finally {
        setLoading(false);
      }
    };

    init();
  }, [autoLoad]);

  // 加载游戏数据
  const loadGames = useCallback(async () => {
    if (!initialized) return;

    try {
      setError(null);
      let result: ExtendedGameData[] = [];

      if (search) {
        // 搜索模式
        result = gameDataManager.searchGames(search, {
          category,
          sortBy,
          locale,
          limit: limit * 10, // 搜索时加载更多结果
        });
      } else if (category) {
        // 分类模式
        result = gameDataManager.getGamesByCategory(category);
      } else if (tag) {
        // 标签模式
        result = gameDataManager.getGamesByTag(tag);
      } else {
        // 全部游戏
        result = gameDataManager.getAllGames();
      }

      // 排序
      result = sortGames(result, sortBy, locale);
      
      setAllGames(result);
      setGames(result.slice(0, limit));
      setCurrentPage(1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load games');
    }
  }, [initialized, search, category, tag, sortBy, locale, limit]);

  // 排序函数
  const sortGames = useCallback((games: ExtendedGameData[], sortType: string, locale: string) => {
    return [...games].sort((a, b) => {
      switch (sortType) {
        case 'rating':
          return b.rating - a.rating;
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'date':
          return (b.addedDate?.getTime() || 0) - (a.addedDate?.getTime() || 0);
        case 'name':
          const nameA = a.title[locale] || a.title.en;
          const nameB = b.title[locale] || b.title.en;
          return nameA.localeCompare(nameB);
        default:
          return (b.popularity || 0) - (a.popularity || 0);
      }
    });
  }, []);

  // 加载更多
  const loadMore = useCallback(() => {
    if (isLoadingMore || games.length >= allGames.length) return;

    setIsLoadingMore(true);
    
    // 模拟异步加载
    setTimeout(() => {
      const nextPage = currentPage + 1;
      const startIndex = (nextPage - 1) * limit;
      const endIndex = startIndex + limit;
      const newGames = allGames.slice(0, endIndex);
      
      setGames(newGames);
      setCurrentPage(nextPage);
      setIsLoadingMore(false);
    }, 300);
  }, [isLoadingMore, games.length, allGames.length, currentPage, limit]);

  // 刷新数据
  const refresh = useCallback(() => {
    gameDataManager.clearCache();
    loadGames();
  }, [loadGames]);

  // 搜索
  const searchGames = useCallback((query: string) => {
    // 这里可以添加防抖逻辑
    loadGames();
  }, [loadGames]);

  // 按分类过滤
  const filterByCategory = useCallback((categoryId: string) => {
    // 这个方法应该由父组件调用，更新options
    loadGames();
  }, [loadGames]);

  // 按标签过滤
  const filterByTag = useCallback((tagName: string) => {
    // 这个方法应该由父组件调用，更新options
    loadGames();
  }, [loadGames]);

  // 排序
  const sortByType = useCallback((sortType: string) => {
    const sorted = sortGames(allGames, sortType, locale);
    setAllGames(sorted);
    setGames(sorted.slice(0, limit));
    setCurrentPage(1);
  }, [allGames, sortGames, locale, limit]);

  // 加载数据
  useEffect(() => {
    loadGames();
  }, [loadGames]);

  // 计算分页信息
  const pagination = useMemo((): PaginationOptions => {
    const totalItems = allGames.length;
    const totalPages = Math.ceil(totalItems / limit);
    
    return {
      page: currentPage,
      pageSize: limit,
      totalPages,
      totalItems,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    };
  }, [allGames.length, limit, currentPage]);

  // 获取统计信息
  const stats = useMemo(() => {
    return gameDataManager.getStats();
  }, [initialized]);

  return {
    games,
    loading,
    error,
    pagination,
    stats,
    loadMore,
    refresh,
    search: searchGames,
    filterByCategory,
    filterByTag,
    sortBy: sortByType,
    hasMore: games.length < allGames.length,
    isLoadingMore,
  };
}

// 热门游戏Hook
export function usePopularGames(limit: number = 20) {
  const [games, setGames] = useState<ExtendedGameData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPopularGames = async () => {
      try {
        setLoading(true);
        await initializeGameData();
        const popularGames = gameDataManager.getPopularGames(limit);
        setGames(popularGames);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load popular games');
      } finally {
        setLoading(false);
      }
    };

    loadPopularGames();
  }, [limit]);

  return { games, loading, error };
}

// 新游戏Hook
export function useNewGames(limit: number = 20) {
  const [games, setGames] = useState<ExtendedGameData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadNewGames = async () => {
      try {
        setLoading(true);
        await initializeGameData();
        const newGames = gameDataManager.getNewGames(limit);
        setGames(newGames);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load new games');
      } finally {
        setLoading(false);
      }
    };

    loadNewGames();
  }, [limit]);

  return { games, loading, error };
}

// 单个游戏Hook
export function useGame(gameId: string) {
  const [game, setGame] = useState<ExtendedGameData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadGame = async () => {
      try {
        setLoading(true);
        await initializeGameData();
        const gameData = gameDataManager.getGameById(gameId);
        setGame(gameData);
        
        if (!gameData) {
          setError('Game not found');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load game');
      } finally {
        setLoading(false);
      }
    };

    if (gameId) {
      loadGame();
    }
  }, [gameId]);

  return { game, loading, error };
}

// 搜索建议Hook
export function useSearchSuggestions(query: string, limit: number = 5) {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    const loadSuggestions = async () => {
      setLoading(true);
      try {
        await initializeGameData();
        const games = gameDataManager.searchGames(query, { limit });
        const suggestions = games.map(game => game.title.en).slice(0, limit);
        setSuggestions(suggestions);
      } catch (err) {
        console.error('Failed to load suggestions:', err);
      } finally {
        setLoading(false);
      }
    };

    // 防抖
    const timer = setTimeout(loadSuggestions, 300);
    return () => clearTimeout(timer);
  }, [query, limit]);

  return { suggestions, loading };
}
