'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  separator?: React.ReactNode;
}

export default function Breadcrumb({
  items,
  className,
  showHome = true,
  separator = <ChevronRight className="h-4 w-4 text-muted-foreground" />
}: BreadcrumbProps) {
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations('Breadcrumb');

  // Auto-generate breadcrumb items from pathname if not provided
  const breadcrumbItems = items || generateBreadcrumbItems(pathname, locale, t);

  if (breadcrumbItems.length === 0) return null;

  return (
    <nav 
      aria-label="Breadcrumb" 
      className={cn('flex items-center space-x-1 text-sm', className)}
    >
      {showHome && (
        <>
          <Link
            href={`/${locale}`}
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
            aria-label={t('home') || 'Home'}
          >
            <Home className="h-4 w-4" />
          </Link>
          {breadcrumbItems.length > 0 && (
            <span className="text-muted-foreground">{separator}</span>
          )}
        </>
      )}
      
      {breadcrumbItems.map((item, index) => {
        const isLast = index === breadcrumbItems.length - 1;
        
        return (
          <React.Fragment key={index}>
            {item.href && !item.isCurrentPage ? (
              <Link
                href={item.href}
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-current={isLast ? 'page' : undefined}
              >
                {item.label}
              </Link>
            ) : (
              <span 
                className={cn(
                  'font-medium',
                  item.isCurrentPage || isLast 
                    ? 'text-foreground' 
                    : 'text-muted-foreground'
                )}
                aria-current={item.isCurrentPage || isLast ? 'page' : undefined}
              >
                {item.label}
              </span>
            )}
            
            {!isLast && (
              <span className="text-muted-foreground">{separator}</span>
            )}
          </React.Fragment>
        );
      })}
    </nav>
  );
}

// Auto-generate breadcrumb items from pathname
function generateBreadcrumbItems(
  pathname: string, 
  locale: string, 
  t: any
): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  
  // Remove locale from segments
  if (segments[0] === locale) {
    segments.shift();
  }
  
  if (segments.length === 0) return [];
  
  const items: BreadcrumbItem[] = [];
  let currentPath = `/${locale}`;
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;
    
    // Get translated label for common segments
    const label = getSegmentLabel(segment, segments, index, t);
    
    items.push({
      label,
      href: isLast ? undefined : currentPath,
      isCurrentPage: isLast
    });
  });
  
  return items;
}

// Get translated label for path segments
function getSegmentLabel(
  segment: string, 
  allSegments: string[], 
  index: number, 
  t: any
): string {
  // Common translations
  const translations: Record<string, string> = {
    'games': t('games') || 'Games',
    'game': t('game') || 'Game',
    'search': t('search') || 'Search',
    'tags': t('tags') || 'Tags',
    'categories': t('categories') || 'Categories',
    'popular': t('popular') || 'Popular',
    'new': t('new') || 'New',
    'featured': t('featured') || 'Featured',
    'action': t('action') || 'Action',
    'adventure': t('adventure') || 'Adventure',
    'racing': t('racing') || 'Racing',
    'shooting': t('shooting') || 'Shooting',
    'puzzle': t('puzzle') || 'Puzzle',
    'strategy': t('strategy') || 'Strategy',
    'sports': t('sports') || 'Sports',
    'simulation': t('simulation') || 'Simulation',
  };
  
  // Check if it's a translated segment
  if (translations[segment]) {
    return translations[segment];
  }
  
  // For game IDs or tag names, format them nicely
  if (allSegments[index - 1] === 'game' || allSegments[index - 1] === 'tags') {
    return formatSegmentName(segment);
  }
  
  // Default: capitalize and format
  return formatSegmentName(segment);
}

// Format segment name for display
function formatSegmentName(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Structured data for breadcrumbs (SEO)
export function BreadcrumbStructuredData({ items }: { items: BreadcrumbItem[] }) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": item.href ? `https://freehubgames.com${item.href}` : undefined
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}

// Hook to get current breadcrumb items
export function useBreadcrumb(): BreadcrumbItem[] {
  const pathname = usePathname();
  const locale = useLocale();
  const t = useTranslations('Breadcrumb');
  
  return generateBreadcrumbItems(pathname, locale, t);
}

// Preset breadcrumb configurations
export const breadcrumbPresets = {
  gameDetail: (gameTitle: string, category: string, locale: string): BreadcrumbItem[] => [
    { label: 'Games', href: `/${locale}/games` },
    { label: category, href: `/${locale}/games/${category.toLowerCase()}` },
    { label: gameTitle, isCurrentPage: true }
  ],
  
  gameCategory: (category: string, locale: string): BreadcrumbItem[] => [
    { label: 'Games', href: `/${locale}/games` },
    { label: category, isCurrentPage: true }
  ],
  
  searchResults: (query: string, locale: string): BreadcrumbItem[] => [
    { label: 'Search', href: `/${locale}/search` },
    { label: `Results for "${query}"`, isCurrentPage: true }
  ],
  
  tagPage: (tagName: string, locale: string): BreadcrumbItem[] => [
    { label: 'Tags', href: `/${locale}/tags` },
    { label: tagName, isCurrentPage: true }
  ]
};
