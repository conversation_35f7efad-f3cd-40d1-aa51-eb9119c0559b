import Link from 'next/link';
import { useLocale } from 'next-intl';

// 标签定义
export const gameTags = {
  // 游戏机制标签
  'multiplayer': { en: 'Multiplayer', zh: '多人游戏' },
  'singleplayer': { en: 'Single Player', zh: '单人游戏' },
  'cooperative': { en: 'Cooperative', zh: '合作游戏' },
  'competitive': { en: 'Competitive', zh: '竞技游戏' },
  
  // 游戏风格标签
  'retro': { en: 'Retro', zh: '复古' },
  'pixel': { en: 'Pixel Art', zh: '像素艺术' },
  '3d': { en: '3D Graphics', zh: '3D画面' },
  '2d': { en: '2D Graphics', zh: '2D画面' },
  
  // 游戏类型标签
  'shooting': { en: 'Shooting', zh: '射击' },
  'platformer': { en: 'Platformer', zh: '平台跳跃' },
  'rpg': { en: 'RPG', zh: '角色扮演' },
  'strategy': { en: 'Strategy', zh: '策略' },
  'puzzle': { en: 'Puzzle', zh: '解谜' },
  'racing': { en: 'Racing', zh: '赛车' },
  'fighting': { en: 'Fighting', zh: '格斗' },
  'adventure': { en: 'Adventure', zh: '冒险' },
  'simulation': { en: 'Simulation', zh: '模拟' },
  'sports': { en: 'Sports', zh: '体育' },
  'action': { en: 'Action', zh: '动作' },
  
  // 难度标签
  'easy': { en: 'Easy', zh: '简单' },
  'medium': { en: 'Medium', zh: '中等' },
  'hard': { en: 'Hard', zh: '困难' },
  'casual': { en: 'Casual', zh: '休闲' },
  'hardcore': { en: 'Hardcore', zh: '硬核' },
  
  // 特殊标签
  'new': { en: 'New', zh: '新游戏' },
  'popular': { en: 'Popular', zh: '热门' },
  'trending': { en: 'Trending', zh: '趋势' },
  'featured': { en: 'Featured', zh: '精选' },
  'mobile-friendly': { en: 'Mobile Friendly', zh: '移动端友好' },
  'no-download': { en: 'No Download', zh: '无需下载' },
  'browser-game': { en: 'Browser Game', zh: '浏览器游戏' },
  'html5': { en: 'HTML5', zh: 'HTML5游戏' }
};

interface GameTagsProps {
  tags: string[];
  maxTags?: number;
  showLinks?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function GameTags({ 
  tags, 
  maxTags = 3, 
  showLinks = false, 
  size = 'sm',
  className = '' 
}: GameTagsProps) {
  const locale = useLocale();
  
  const displayTags = tags.slice(0, maxTags);
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const getTagDisplayName = (tag: string) => {
    return gameTags[tag as keyof typeof gameTags]?.[locale as keyof typeof gameTags[keyof typeof gameTags]] || 
           gameTags[tag as keyof typeof gameTags]?.['en'] || 
           tag;
  };

  if (displayTags.length === 0) return null;

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {displayTags.map((tag) => {
        const displayName = getTagDisplayName(tag);
        
        if (showLinks) {
          return (
            <Link
              key={tag}
              href={`/${locale}/tags/${tag}`}
              className={`inline-flex items-center rounded-full bg-secondary hover:bg-primary hover:text-primary-foreground transition-colors ${sizeClasses[size]}`}
            >
              {displayName}
            </Link>
          );
        }
        
        return (
          <span
            key={tag}
            className={`inline-flex items-center rounded-full bg-secondary text-secondary-foreground ${sizeClasses[size]}`}
          >
            {displayName}
          </span>
        );
      })}
      {tags.length > maxTags && (
        <span className={`inline-flex items-center rounded-full bg-muted text-muted-foreground ${sizeClasses[size]}`}>
          +{tags.length - maxTags}
        </span>
      )}
    </div>
  );
}
