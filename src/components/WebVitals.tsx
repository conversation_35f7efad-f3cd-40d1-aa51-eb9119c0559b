'use client';

import { useEffect } from 'react';

// Core Web Vitals monitoring
export default function WebVitals() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    // Import web-vitals library dynamically
    import('web-vitals').then((webVitals) => {
      // Largest Contentful Paint
      webVitals.onLCP((metric) => {
        console.log('LCP:', metric);
        sendToAnalytics('LCP', metric);
      });

      // Interaction to Next Paint (replaces FID)
      webVitals.onINP((metric) => {
        console.log('INP:', metric);
        sendToAnalytics('INP', metric);
      });

      // Cumulative Layout Shift
      webVitals.onCLS((metric) => {
        console.log('CLS:', metric);
        sendToAnalytics('CLS', metric);
      });

      // First Contentful Paint
      webVitals.onFCP((metric) => {
        console.log('FCP:', metric);
        sendToAnalytics('FCP', metric);
      });

      // Time to First Byte
      webVitals.onTTFB((metric) => {
        console.log('TTFB:', metric);
        sendToAnalytics('TTFB', metric);
      });
    }).catch(error => {
      console.error('Failed to load web-vitals:', error);
    });

    // Custom performance monitoring
    monitorCustomMetrics();
  }, []);

  return null;
}

// Send metrics to analytics
function sendToAnalytics(metricName: string, metric: any) {
  // Send to Google Analytics 4
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', metricName, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    });
  }

  // Send to Umami (if available)
  if (typeof window !== 'undefined' && (window as any).umami) {
    (window as any).umami.track('web-vital', {
      metric: metricName,
      value: metric.value,
      rating: metric.rating,
    });
  }

  // Send to custom analytics endpoint
  fetch('/api/analytics/web-vitals', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      metric: metricName,
      value: metric.value,
      rating: metric.rating,
      id: metric.id,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    }),
  }).catch(error => {
    console.error('Failed to send analytics:', error);
  });
}

// Monitor custom performance metrics
function monitorCustomMetrics() {
  if (typeof window === 'undefined') return;

  // Monitor page load time
  window.addEventListener('load', () => {
    const loadTime = performance.now();
    console.log('Page load time:', loadTime);
    
    // Send custom metric
    sendCustomMetric('page_load_time', loadTime);
  });

  // Monitor navigation timing
  if ('navigation' in performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (navigation) {
      const metrics = {
        dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp_connection: navigation.connectEnd - navigation.connectStart,
        server_response: navigation.responseEnd - navigation.requestStart,
        dom_processing: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        resource_loading: navigation.loadEventEnd - navigation.domContentLoadedEventEnd,
      };

      Object.entries(metrics).forEach(([name, value]) => {
        console.log(`${name}:`, value);
        sendCustomMetric(name, value);
      });
    }
  }

  // Monitor resource loading
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'resource') {
        const resource = entry as PerformanceResourceTiming;
        
        // Monitor slow resources
        if (resource.duration > 1000) {
          console.warn('Slow resource:', resource.name, resource.duration);
          sendCustomMetric('slow_resource', resource.duration, {
            resource_name: resource.name,
            resource_type: resource.initiatorType,
          });
        }
      }
    });
  });

  observer.observe({ entryTypes: ['resource'] });

  // Monitor long tasks
  if ('PerformanceObserver' in window) {
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.warn('Long task detected:', entry.duration);
          sendCustomMetric('long_task', entry.duration);
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      console.log('Long task monitoring not supported');
    }
  }

  // Monitor memory usage (if available)
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    const memoryMetrics = {
      used_heap: memory.usedJSHeapSize,
      total_heap: memory.totalJSHeapSize,
      heap_limit: memory.jsHeapSizeLimit,
    };

    Object.entries(memoryMetrics).forEach(([name, value]) => {
      sendCustomMetric(`memory_${name}`, value);
    });
  }
}

// Send custom metrics
function sendCustomMetric(name: string, value: number, additionalData?: any) {
  // Send to analytics
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', 'custom_metric', {
      event_category: 'Performance',
      event_label: name,
      value: Math.round(value),
      custom_parameter_1: additionalData ? JSON.stringify(additionalData) : undefined,
    });
  }

  // Send to Umami
  if (typeof window !== 'undefined' && (window as any).umami) {
    (window as any).umami.track('performance-metric', {
      metric: name,
      value: value,
      ...additionalData,
    });
  }
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Monitor component render time
    const startTime = performance.now();

    return () => {
      const renderTime = performance.now() - startTime;
      if (renderTime > 16) { // More than one frame
        console.log('Component render time:', renderTime);
        sendCustomMetric('component_render_time', renderTime);
      }
    };
  }, []);
}

// Real User Monitoring (RUM) data collection
export function collectRUMData() {
  if (typeof window === 'undefined') return;

  const rumData = {
    timestamp: Date.now(),
    url: window.location.href,
    referrer: document.referrer,
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
    },
    connection: (navigator as any).connection ? {
      effectiveType: (navigator as any).connection.effectiveType,
      downlink: (navigator as any).connection.downlink,
      rtt: (navigator as any).connection.rtt,
    } : null,
    deviceMemory: (navigator as any).deviceMemory || null,
    hardwareConcurrency: navigator.hardwareConcurrency || null,
  };

  // Send RUM data
  fetch('/api/analytics/rum', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(rumData),
  }).catch(error => {
    console.error('Failed to send RUM data:', error);
  });
}
