"use client";

import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, ChevronDown, Search, X, TrendingUp, Star, Gamepad2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON>et<PERSON><PERSON>le,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { ThemeToggle } from "@/components/ThemeToggle";
import { gamesData } from "@/data/games";
import { gameDetailsData } from "@/data/game-details";


interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      examples: string;
      docs: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
    };
  };
}

export default function Header({ header }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{
    id: string;
    title: string;
    category: string;
    image: string;
  }>>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('Navigation');
  const tHome = useTranslations('Home');

  // 监听页面滚动，添加背景效果
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理搜索
  useEffect(() => {
    if (searchQuery.trim()) {
      const results: Array<{
        id: string;
        title: string;
        category: string;
        image: string;
      }> = [];

      // 遍历所有游戏数据，限制结果数量以提高性能
      Object.entries(gamesData).forEach(([category, games]) => {
        games.forEach(game => {
          if (results.length >= 8) return; // 限制快速搜索结果数量

          const gameDetail = gameDetailsData[game.id];
          if (gameDetail) {
            const title = gameDetail.title[currentLocale as keyof typeof gameDetail.title];
            const categoryName = gameDetail.category[currentLocale as keyof typeof gameDetail.category];

            // 根据当前语言搜索标题和分类
            if (
              title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              categoryName.toLowerCase().includes(searchQuery.toLowerCase())
            ) {
              results.push({
                id: game.id,
                title,
                category: categoryName,
                image: game.image
              });
            }
          }
        });
      });

      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, currentLocale]);

  // 处理搜索提交
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/${currentLocale}/search?q=${encodeURIComponent(searchQuery.trim())}`;
      setSearchOpen(false);
      setSearchQuery('');
      setSearchResults([]);
    }
  };

  // 点击外部关闭搜索
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setSearchOpen(false);
      }
    };

    if (searchOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [searchOpen]);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  // 游戏分类数据
  const gameCategories = [
    { id: 'action', name: t('categoryList.action'), icon: '🎮' },
    { id: 'adventure', name: t('categoryList.adventure'), icon: '🗺️' },
    { id: 'racing', name: t('categoryList.racing'), icon: '🏎️' },
    { id: 'shooting', name: t('categoryList.shooting'), icon: '🔫' },
    { id: 'horror', name: t('categoryList.horror'), icon: '👻' },
    { id: 'strategy', name: t('categoryList.strategy'), icon: '⚔️' },
    { id: 'sports', name: t('categoryList.sports'), icon: '⚽' },
    { id: 'simulation', name: t('categoryList.simulation'), icon: '🎯' },
    { id: 'puzzle', name: t('categoryList.puzzle'), icon: '🧩' },
    { id: 'sandbox', name: t('categoryList.sandbox'), icon: '🏗️' }
  ];

  return (
    <>
      {/* Left Sidebar Navigation - Desktop */}
      <nav className="hidden lg:flex fixed top-0 left-0 w-64 h-full bg-background border-r border-border/50 z-50">
        <div className="flex flex-col w-full">
          {/* Logo Section */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="p-6 border-b border-border/50"
          >
            <Link href={`/${currentLocale}`} className="flex items-center gap-3" aria-label={t('home')}>
              <Image
                src="/logo.png"
                alt="FreeHubGames Logo"
                width={40}
                height={40}
                className="rounded-full"
                priority
              />
              <div className="flex items-center text-xl font-bold" role="banner">
                <span>Free</span>
                <span>Hub</span>
                <span className="text-gradient">Games</span>
              </div>
            </Link>
          </motion.div>



          {/* Main Navigation */}
          <div className="flex-1 overflow-y-auto scrollbar-hide">
            <div className="p-4">
              <ul className="space-y-2">
                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Link
                    href={`/${currentLocale}`}
                    className="flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20"
                  >
                    <span className="text-base">🏠</span>
                    <span className="font-medium">{t('home')}</span>
                  </Link>
                </motion.li>

                {/* <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                >
                  <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all text-muted-foreground hover:text-foreground hover:bg-secondary/60">
                    <span className="text-base">🕒</span>
                    <span className="font-medium">Recent</span>
                  </button>
                </motion.li>

                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all text-muted-foreground hover:text-foreground hover:bg-secondary/60">
                    <TrendingUp className="h-4 w-4" />
                    <span className="font-medium">Trending Games</span>
                  </button>
                </motion.li>

                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                >
                  <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all text-muted-foreground hover:text-foreground hover:bg-secondary/60">
                    <Star className="h-4 w-4" />
                    <span className="font-medium">Latest Games</span>
                  </button>
                </motion.li>

                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                >
                  <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all text-muted-foreground hover:text-foreground hover:bg-secondary/60">
                    <Gamepad2 className="h-4 w-4" />
                    <span className="font-medium">Featured Games</span>
                  </button>
                </motion.li> */}

                <motion.li
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <Link
                    href={`/${currentLocale}/games`}
                    className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-left transition-all text-muted-foreground hover:text-foreground hover:bg-secondary/60"
                  >
                    <span className="text-base">🎯</span>
                    <span className="font-medium">All Games</span>
                  </Link>
                </motion.li>
              </ul>

              {/* Divider */}
              <div className="my-4 border-t border-border/50"></div>

              {/* Game Categories */}
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-muted-foreground px-3 py-1 uppercase tracking-wide">
                  {t('categories')}
                </h3>
                <ul className="space-y-1">
                  {gameCategories.map((category, index) => (
                    <motion.li
                      key={category.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.6 + index * 0.05 }}
                    >
                      <Link
                        href={`/${currentLocale}/games/${category.id}`}
                        className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-all text-muted-foreground hover:text-foreground hover:bg-secondary/60"
                      >
                        <span className="text-sm">{category.icon}</span>
                        <span className="font-medium text-sm">{category.name}</span>
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Bottom Section - Language & Theme */}
          <div className="p-4 border-t border-border/50">
            <div className="flex flex-col space-y-3">
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors px-3 py-2 rounded-lg hover:bg-secondary w-full justify-start">
                  <Globe className="h-4 w-4" />
                  <span>{localeNames[currentLocale]}</span>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  {locales.map((locale) => (
                    <DropdownMenuItem
                      key={locale}
                      onClick={() => switchLocale(locale)}
                      className="cursor-pointer"
                    >
                      {localeNames[locale]}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <div className="flex justify-start">
                <ThemeToggle />
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Header - Top */}
      <div className="lg:hidden w-full bg-background border-b border-border/50">
        {/* Top Row - Logo and Menu */}
        <div className="px-4 sm:px-6 py-4">
          <nav className="relative flex items-center justify-between">
            {/* Mobile Logo */}
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex-none"
            >
              <Link href={`/${currentLocale}`} className="flex items-center gap-2" aria-label={t('home')}>
                <Image
                  src="/logo.png"
                  alt="FreeHubGames Logo"
                  width={32}
                  height={32}
                  className="rounded-full"
                  priority
                />
                <div className="flex items-center text-lg font-bold" role="banner">
                  <span>Free</span>
                  <span>Hub</span>
                  <span className="text-gradient">Games</span>
                </div>
              </Link>
            </motion.div>

            {/* Mobile Menu Button */}
            <div className="flex items-center space-x-3">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-9 w-9 hover:bg-secondary">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
              <SheetContent side="right" className="border-l border-border">
                <SheetHeader>
                  <SheetTitle className="flex items-center">
                    <span className="text-xl font-bold">Free</span>
                    <span className="text-xl font-bold">Hub</span>
                    <span className="text-xl font-bold text-gradient">Games</span>
                  </SheetTitle>
                </SheetHeader>
                <div className="flex flex-col space-y-4 mt-6">
                  <Link
                    href={`/${currentLocale}`}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {t('home')}
                  </Link>

                  <div className="block">
                    <details className="group">
                      <summary className="list-none flex justify-between items-center cursor-pointer text-sm text-muted-foreground hover:text-foreground transition-colors">
                        {t('categories')} <ChevronDown className="h-4 w-4 group-open:rotate-180 transition-transform" />
                      </summary>
                      <div className="mt-2 ml-4 grid gap-2">
                        {gameCategories.map(category => (
                          <Link
                            key={category.id}
                            href={`/${currentLocale}/games/${category.id}`}
                            className="flex items-center gap-2 py-2 px-3 text-sm text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-secondary"
                          >
                            <span className="text-base">{category.icon}</span>
                            <span>{category.name}</span>
                          </Link>
                        ))}
                      </div>
                    </details>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors mt-2">
                      <Globe className="h-4 w-4" />
                      <span>{localeNames[currentLocale]}</span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {locales.map((locale) => (
                        <DropdownMenuItem
                          key={locale}
                          onClick={() => switchLocale(locale)}
                          className="cursor-pointer"
                        >
                          {localeNames[locale]}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </SheetContent>
            </Sheet>
            </div>
          </nav>
        </div>

        {/* Search Bar Row */}
        <div className="px-4 sm:px-6 pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('search')}
              className="w-full pl-10 pr-4 py-2.5 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all text-sm"
              onFocus={() => setSearchOpen(true)}
            />
          </div>
        </div>
      </div>

      {/* Desktop Search Bar - Fixed at top */}
      <div className="hidden lg:block fixed top-0 left-64 right-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border/50">
        <div className="px-6 py-3">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('search')}
              className="w-full pl-10 pr-4 py-2 rounded-lg border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all text-sm"
              onFocus={() => setSearchOpen(true)}
            />
          </div>
        </div>
      </div>

      {/* 搜索弹出层 - 通用 */}
      {searchOpen && (
        <motion.div
          ref={searchRef}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="fixed top-20 lg:top-16 left-4 right-4 lg:left-64 lg:right-auto lg:w-96 z-50 bg-card shadow-xl border border-border rounded-xl p-6 max-h-[80vh] overflow-y-auto"
        >
          {/* 关闭按钮 */}
          <button
            onClick={() => {
              setSearchOpen(false);
              setSearchQuery('');
              setSearchResults([]);
            }}
            className="absolute top-4 right-4 p-1 hover:bg-secondary rounded-full transition-colors"
          >
            <X className="h-4 w-4" />
          </button>

          {/* 搜索结果 */}
          {searchResults.length > 0 && (
            <div className="mb-6">
              <h3 className="text-sm font-semibold mb-3 text-foreground">{t('searchResults')}</h3>
              <div className="space-y-2">
                {searchResults.map((result) => (
                  <Link
                    key={result.id}
                    href={`/${currentLocale}/game/${result.id}`}
                    className="flex items-center space-x-3 p-3 hover:bg-secondary rounded-lg transition-colors"
                    onClick={() => {
                      setSearchOpen(false);
                      setSearchQuery('');
                      setSearchResults([]);
                    }}
                  >
                    <div className="w-12 h-12 bg-secondary rounded-lg flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{result.title}</p>
                      <p className="text-xs text-muted-foreground truncate">{result.category}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* 游戏分类 */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold mb-3 text-foreground">{t('categories')}</h3>
            <div className="grid grid-cols-2 gap-2">
              {gameCategories.slice(0, 6).map((category) => (
                <Link
                  key={category.id}
                  href={`/${currentLocale}/games/${category.id}`}
                  className="flex items-center gap-2 p-2 hover:bg-secondary rounded-lg transition-colors"
                  onClick={() => setSearchOpen(false)}
                >
                  <span className="text-base">{category.icon}</span>
                  <span className="text-sm font-medium truncate">{category.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* 热门游戏 */}
          <div>
            <h3 className="text-sm font-semibold mb-3 text-foreground">Trending Games</h3>
            <div className="space-y-2">
              {/* 模拟热门游戏数据 */}
              {[
                { id: 1, title: "Super Mario Bros", category: "Action", rating: "4.8" },
                { id: 2, title: "Tetris Classic", category: "Puzzle", rating: "4.7" },
                { id: 3, title: "Racing Thunder", category: "Racing", rating: "4.6" },
                { id: 4, title: "Space Shooter", category: "Shooter", rating: "4.5" }
              ].map((game) => (
                <Link
                  key={game.id}
                  href={`/${currentLocale}/game/${game.id}`}
                  className="flex items-center space-x-3 p-2 hover:bg-secondary rounded-lg transition-colors"
                  onClick={() => setSearchOpen(false)}
                >
                  <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex-shrink-0 flex items-center justify-center">
                    <Gamepad2 className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{game.title}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{game.category}</span>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        <Star className="h-3 w-3 fill-current text-yellow-400" />
                        {game.rating}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* 无搜索结果时的提示 */}
          {searchQuery && searchResults.length === 0 && (
            <div className="text-center py-4">
              <p className="text-sm text-muted-foreground">{t('noSearchResults')}</p>
            </div>
          )}
        </motion.div>
      )}
    </>
  );
}