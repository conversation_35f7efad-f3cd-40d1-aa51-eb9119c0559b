'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

// Touch gesture types
export interface TouchGesture {
  type: 'tap' | 'double-tap' | 'long-press' | 'swipe' | 'pinch' | 'pan';
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  deltaX: number;
  deltaY: number;
  distance: number;
  duration: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  scale?: number;
}

// Touch gesture options
export interface TouchGestureOptions {
  onTap?: (gesture: TouchGesture) => void;
  onDoubleTap?: (gesture: TouchGesture) => void;
  onLongPress?: (gesture: TouchGesture) => void;
  onSwipe?: (gesture: TouchGesture) => void;
  onPinch?: (gesture: TouchGesture) => void;
  onPan?: (gesture: TouchGesture) => void;
  longPressDelay?: number;
  doubleTapDelay?: number;
  swipeThreshold?: number;
  pinchThreshold?: number;
  preventDefault?: boolean;
}

// Touch gesture hook
export function useTouchGestures(
  elementRef: React.RefObject<HTMLElement>,
  options: TouchGestureOptions = {}
) {
  const {
    onTap,
    onDoubleTap,
    onLongPress,
    onSwipe,
    onPinch,
    onPan,
    longPressDelay = 500,
    doubleTapDelay = 300,
    swipeThreshold = 50,
    pinchThreshold = 0.1,
    preventDefault = true,
  } = options;

  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const lastTapRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const initialDistanceRef = useRef<number>(0);
  const isPinchingRef = useRef<boolean>(false);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (preventDefault) {
      e.preventDefault();
    }

    const touch = e.touches[0];
    const now = Date.now();
    
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: now,
    };

    // Handle multi-touch for pinch
    if (e.touches.length === 2) {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      initialDistanceRef.current = distance;
      isPinchingRef.current = true;
    } else {
      isPinchingRef.current = false;
    }

    // Start long press timer
    if (onLongPress) {
      longPressTimerRef.current = setTimeout(() => {
        if (touchStartRef.current) {
          const gesture: TouchGesture = {
            type: 'long-press',
            startX: touchStartRef.current.x,
            startY: touchStartRef.current.y,
            endX: touchStartRef.current.x,
            endY: touchStartRef.current.y,
            deltaX: 0,
            deltaY: 0,
            distance: 0,
            duration: Date.now() - touchStartRef.current.time,
          };
          onLongPress(gesture);
        }
      }, longPressDelay);
    }
  }, [onLongPress, longPressDelay, preventDefault]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (preventDefault) {
      e.preventDefault();
    }

    if (!touchStartRef.current) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;

    // Clear long press timer on move
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && isPinchingRef.current && onPinch) {
      const touch1 = e.touches[0];
      const touch2 = e.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );
      
      const scale = currentDistance / initialDistanceRef.current;
      
      if (Math.abs(scale - 1) > pinchThreshold) {
        const gesture: TouchGesture = {
          type: 'pinch',
          startX: touchStartRef.current.x,
          startY: touchStartRef.current.y,
          endX: touch.clientX,
          endY: touch.clientY,
          deltaX,
          deltaY,
          distance: currentDistance,
          duration: Date.now() - touchStartRef.current.time,
          scale,
        };
        onPinch(gesture);
      }
    }

    // Handle pan gesture
    if (onPan && !isPinchingRef.current) {
      const gesture: TouchGesture = {
        type: 'pan',
        startX: touchStartRef.current.x,
        startY: touchStartRef.current.y,
        endX: touch.clientX,
        endY: touch.clientY,
        deltaX,
        deltaY,
        distance: Math.sqrt(deltaX * deltaX + deltaY * deltaY),
        duration: Date.now() - touchStartRef.current.time,
      };
      onPan(gesture);
    }
  }, [onPinch, onPan, pinchThreshold, preventDefault]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (preventDefault) {
      e.preventDefault();
    }

    if (!touchStartRef.current) return;

    const touch = e.changedTouches[0];
    const now = Date.now();
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const duration = now - touchStartRef.current.time;

    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    // Determine gesture type
    if (distance > swipeThreshold && onSwipe) {
      // Swipe gesture
      let direction: 'up' | 'down' | 'left' | 'right';
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        direction = deltaX > 0 ? 'right' : 'left';
      } else {
        direction = deltaY > 0 ? 'down' : 'up';
      }

      const gesture: TouchGesture = {
        type: 'swipe',
        startX: touchStartRef.current.x,
        startY: touchStartRef.current.y,
        endX: touch.clientX,
        endY: touch.clientY,
        deltaX,
        deltaY,
        distance,
        duration,
        direction,
      };
      onSwipe(gesture);
    } else if (distance < 10 && duration < 300) {
      // Tap or double tap
      const currentTap = {
        x: touch.clientX,
        y: touch.clientY,
        time: now,
      };

      if (lastTapRef.current && 
          now - lastTapRef.current.time < doubleTapDelay &&
          Math.abs(currentTap.x - lastTapRef.current.x) < 20 &&
          Math.abs(currentTap.y - lastTapRef.current.y) < 20) {
        // Double tap
        if (onDoubleTap) {
          const gesture: TouchGesture = {
            type: 'double-tap',
            startX: touchStartRef.current.x,
            startY: touchStartRef.current.y,
            endX: touch.clientX,
            endY: touch.clientY,
            deltaX,
            deltaY,
            distance,
            duration,
          };
          onDoubleTap(gesture);
        }
        lastTapRef.current = null;
      } else {
        // Single tap
        lastTapRef.current = currentTap;
        setTimeout(() => {
          if (lastTapRef.current === currentTap && onTap) {
            const gesture: TouchGesture = {
              type: 'tap',
              startX: touchStartRef.current!.x,
              startY: touchStartRef.current!.y,
              endX: touch.clientX,
              endY: touch.clientY,
              deltaX,
              deltaY,
              distance,
              duration,
            };
            onTap(gesture);
          }
        }, doubleTapDelay);
      }
    }

    touchStartRef.current = null;
    isPinchingRef.current = false;
  }, [onTap, onDoubleTap, onSwipe, swipeThreshold, doubleTapDelay, preventDefault]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
    };
  }, []);
}

// Touch-optimized button component
interface TouchButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  onLongPress?: () => void;
  className?: string;
  disabled?: boolean;
  hapticFeedback?: boolean;
}

export function TouchButton({
  children,
  onClick,
  onLongPress,
  className = '',
  disabled = false,
  hapticFeedback = true,
}: TouchButtonProps) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isPressed, setIsPressed] = useState(false);

  const triggerHapticFeedback = useCallback(() => {
    if (hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10); // Short vibration
    }
  }, [hapticFeedback]);

  useTouchGestures(buttonRef, {
    onTap: () => {
      if (!disabled && onClick) {
        triggerHapticFeedback();
        onClick();
      }
    },
    onLongPress: () => {
      if (!disabled && onLongPress) {
        triggerHapticFeedback();
        onLongPress();
      }
    },
  });

  return (
    <button
      ref={buttonRef}
      className={`touch-manipulation select-none transition-all duration-150 ${
        isPressed ? 'scale-95' : 'scale-100'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${className}`}
      disabled={disabled}
      onTouchStart={() => setIsPressed(true)}
      onTouchEnd={() => setIsPressed(false)}
      onTouchCancel={() => setIsPressed(false)}
    >
      {children}
    </button>
  );
}
