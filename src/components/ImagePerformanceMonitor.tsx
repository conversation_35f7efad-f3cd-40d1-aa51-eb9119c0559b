'use client';

import { useEffect, useRef } from 'react';

interface ImageLoadMetrics {
  url: string;
  loadTime: number;
  size: number;
  fromCache: boolean;
  timestamp: number;
}

class ImagePerformanceMonitor {
  private static instance: ImagePerformanceMonitor;
  private metrics: ImageLoadMetrics[] = [];
  private observers: PerformanceObserver[] = [];

  static getInstance(): ImagePerformanceMonitor {
    if (!ImagePerformanceMonitor.instance) {
      ImagePerformanceMonitor.instance = new ImagePerformanceMonitor();
    }
    return ImagePerformanceMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    // 监控资源加载性能
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          // 类型断言为 PerformanceResourceTiming
          const resourceEntry = entry as PerformanceResourceTiming;
          if (resourceEntry.initiatorType === 'img' || entry.name.includes('/api/image-proxy')) {
            this.recordImageLoad({
              url: entry.name,
              loadTime: entry.duration,
              size: resourceEntry.transferSize || 0,
              fromCache: resourceEntry.transferSize === 0,
              timestamp: Date.now()
            });
          }
        });
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (error) {
        console.warn('Failed to observe resource performance:', error);
      }
    }
  }

  private recordImageLoad(metrics: ImageLoadMetrics) {
    this.metrics.push(metrics);
    
    // 保持最近1000条记录
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // 在开发环境下输出性能信息
    if (process.env.NODE_ENV === 'development') {
      console.log('Image Load Metrics:', {
        url: metrics.url.split('/').pop(),
        loadTime: `${metrics.loadTime.toFixed(2)}ms`,
        size: `${(metrics.size / 1024).toFixed(2)}KB`,
        fromCache: metrics.fromCache ? 'Yes' : 'No'
      });
    }
  }

  getMetrics(): ImageLoadMetrics[] {
    return [...this.metrics];
  }

  getAverageLoadTime(): number {
    if (this.metrics.length === 0) return 0;
    const total = this.metrics.reduce((sum, metric) => sum + metric.loadTime, 0);
    return total / this.metrics.length;
  }

  getCacheHitRate(): number {
    if (this.metrics.length === 0) return 0;
    const cacheHits = this.metrics.filter(metric => metric.fromCache).length;
    return (cacheHits / this.metrics.length) * 100;
  }

  getSlowImages(threshold: number = 1000): ImageLoadMetrics[] {
    return this.metrics.filter(metric => metric.loadTime > threshold);
  }

  generateReport(): {
    totalImages: number;
    averageLoadTime: number;
    cacheHitRate: number;
    slowImages: number;
    totalDataTransferred: number;
  } {
    const slowImages = this.getSlowImages();
    const totalDataTransferred = this.metrics.reduce((sum, metric) => sum + metric.size, 0);

    return {
      totalImages: this.metrics.length,
      averageLoadTime: this.getAverageLoadTime(),
      cacheHitRate: this.getCacheHitRate(),
      slowImages: slowImages.length,
      totalDataTransferred
    };
  }

  clearMetrics() {
    this.metrics = [];
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
  }
}

// React Hook for using the performance monitor
export function useImagePerformanceMonitor() {
  const monitorRef = useRef<ImagePerformanceMonitor>();

  useEffect(() => {
    monitorRef.current = ImagePerformanceMonitor.getInstance();
    
    return () => {
      // Don't destroy the singleton, just clean up the reference
      monitorRef.current = undefined;
    };
  }, []);

  return {
    getMetrics: () => monitorRef.current?.getMetrics() || [],
    getAverageLoadTime: () => monitorRef.current?.getAverageLoadTime() || 0,
    getCacheHitRate: () => monitorRef.current?.getCacheHitRate() || 0,
    getSlowImages: (threshold?: number) => monitorRef.current?.getSlowImages(threshold) || [],
    generateReport: () => monitorRef.current?.generateReport() || {
      totalImages: 0,
      averageLoadTime: 0,
      cacheHitRate: 0,
      slowImages: 0,
      totalDataTransferred: 0
    },
    clearMetrics: () => monitorRef.current?.clearMetrics()
  };
}

// Performance monitoring component
export default function ImagePerformanceMonitorComponent() {
  const monitor = useImagePerformanceMonitor();

  useEffect(() => {
    // 在开发环境下定期输出性能报告
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        const report = monitor.generateReport();
        if (report.totalImages > 0) {
          console.group('🖼️ Image Performance Report');
          console.log(`Total Images Loaded: ${report.totalImages}`);
          console.log(`Average Load Time: ${report.averageLoadTime.toFixed(2)}ms`);
          console.log(`Cache Hit Rate: ${report.cacheHitRate.toFixed(1)}%`);
          console.log(`Slow Images (>1s): ${report.slowImages}`);
          console.log(`Total Data Transferred: ${(report.totalDataTransferred / 1024 / 1024).toFixed(2)}MB`);
          console.groupEnd();
        }
      }, 30000); // 每30秒输出一次报告

      return () => clearInterval(interval);
    }
  }, [monitor]);

  // 这个组件不渲染任何内容，只是用于监控
  return null;
}

// 导出性能监控器实例
export { ImagePerformanceMonitor };
