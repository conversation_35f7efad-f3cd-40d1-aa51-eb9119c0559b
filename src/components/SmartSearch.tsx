'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Filter, TrendingUp, Clock, Star } from 'lucide-react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { ExtendedGameData } from '@/config/gameDataConfig';
import { gameDataConfig } from '@/config/gameDataConfig';
import { useSearchSuggestions } from '@/hooks/useGameData';
import { gameDataManager } from '@/lib/gameDataManager';
import OptimizedImage from './OptimizedImage';

interface SmartSearchProps {
  onSearch?: (query: string) => void;
  onGameSelect?: (game: ExtendedGameData) => void;
  placeholder?: string;
  className?: string;
  showFilters?: boolean;
  showSuggestions?: boolean;
  maxSuggestions?: number;
  autoFocus?: boolean;
}

interface SearchFilters {
  category?: string;
  minRating?: number;
  tags?: string[];
  sortBy?: 'relevance' | 'rating' | 'popularity' | 'name';
}

export default function SmartSearch({
  onSearch,
  onGameSelect,
  placeholder,
  className = '',
  showFilters = true,
  showSuggestions = true,
  maxSuggestions = 8,
  autoFocus = false,
}: SmartSearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<ExtendedGameData[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();
  
  const t = useTranslations('Search');
  const locale = useLocale();
  const router = useRouter();

  // 获取搜索建议
  const { suggestions, loading: suggestionsLoading } = useSearchSuggestions(
    query, 
    maxSuggestions
  );

  // 热门搜索词
  const popularSearches = useMemo(() => [
    'action games',
    'puzzle games',
    'racing games',
    'shooting games',
    'adventure games',
  ], []);

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(true);

    // 防抖搜索
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      if (value.trim() && value.length >= gameDataConfig.search.minSearchLength) {
        performSearch(value);
      } else {
        setSearchResults([]);
      }
    }, gameDataConfig.performance.debounceDelay);
  }, []);

  // 执行搜索
  const performSearch = useCallback(async (searchQuery: string) => {
    setIsSearching(true);
    try {
      const results = gameDataManager.searchGames(searchQuery, {
        category: filters.category,
        sortBy: filters.sortBy || 'relevance',
        locale,
        limit: maxSuggestions,
      });
      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [filters, locale, maxSuggestions]);

  // 处理搜索提交
  const handleSearch = useCallback((searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    // 添加到最近搜索
    const newRecentSearches = [
      searchQuery,
      ...recentSearches.filter(s => s !== searchQuery)
    ].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));

    // 执行搜索
    onSearch?.(searchQuery);
    setIsOpen(false);

    // 导航到搜索页面
    const params = new URLSearchParams();
    params.set('q', searchQuery);
    if (filters.category) params.set('category', filters.category);
    if (filters.sortBy) params.set('sort', filters.sortBy);
    router.push(`/${locale}/search?${params.toString()}`);
  }, [query, recentSearches, onSearch, filters, locale, router]);

  // 处理游戏选择
  const handleGameSelect = useCallback((game: ExtendedGameData) => {
    onGameSelect?.(game);
    setIsOpen(false);
    router.push(`/${locale}/game/${game.id}`);
  }, [onGameSelect, locale, router]);

  // 清除搜索
  const clearSearch = useCallback(() => {
    setQuery('');
    setSearchResults([]);
    setIsOpen(false);
    inputRef.current?.focus();
  }, []);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch();
    } else if (e.key === 'Escape') {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  }, [handleSearch]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 加载最近搜索
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }
  }, []);

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // 渲染搜索建议
  const renderSuggestions = () => {
    if (!showSuggestions || !isOpen) return null;

    const hasQuery = query.trim().length >= gameDataConfig.search.minSearchLength;
    const hasResults = searchResults.length > 0;
    const hasSuggestions = suggestions.length > 0;

    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute top-full left-0 right-0 mt-2 bg-background border border-border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
        >
          {/* 搜索结果 */}
          {hasQuery && hasResults && (
            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2 px-2">
                {t('searchResults') || 'Search Results'}
              </div>
              {searchResults.map((game) => (
                <button
                  key={game.id}
                  onClick={() => handleGameSelect(game)}
                  className="w-full flex items-center space-x-3 p-2 rounded-md hover:bg-secondary transition-colors text-left"
                >
                  <OptimizedImage
                    src={game.image}
                    alt={game.title[locale] || game.title.en}
                    width={40}
                    height={24}
                    className="rounded object-cover flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {game.title[locale] || game.title.en}
                    </div>
                    <div className="text-xs text-muted-foreground flex items-center space-x-2">
                      <span>{game.category[locale] || game.category.en}</span>
                      <Star className="h-3 w-3" />
                      <span>{game.rating}</span>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* 搜索建议 */}
          {hasQuery && hasSuggestions && !hasResults && (
            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2 px-2">
                {t('suggestions') || 'Suggestions'}
              </div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSearch(suggestion)}
                  className="w-full flex items-center space-x-2 p-2 rounded-md hover:bg-secondary transition-colors text-left"
                >
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{suggestion}</span>
                </button>
              ))}
            </div>
          )}

          {/* 最近搜索 */}
          {!hasQuery && recentSearches.length > 0 && (
            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2 px-2 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {t('recentSearches') || 'Recent Searches'}
              </div>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleSearch(search)}
                  className="w-full flex items-center space-x-2 p-2 rounded-md hover:bg-secondary transition-colors text-left"
                >
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{search}</span>
                </button>
              ))}
            </div>
          )}

          {/* 热门搜索 */}
          {!hasQuery && (
            <div className="p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2 px-2 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                {t('popularSearches') || 'Popular Searches'}
              </div>
              {popularSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleSearch(search)}
                  className="w-full flex items-center space-x-2 p-2 rounded-md hover:bg-secondary transition-colors text-left"
                >
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{search}</span>
                </button>
              ))}
            </div>
          )}

          {/* 加载状态 */}
          {(isSearching || suggestionsLoading) && (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
            </div>
          )}

          {/* 无结果 */}
          {hasQuery && !hasResults && !isSearching && !suggestionsLoading && (
            <div className="p-4 text-center text-muted-foreground">
              <div className="text-sm">{t('noResults') || 'No results found'}</div>
              <div className="text-xs mt-1">
                {t('tryDifferentKeywords') || 'Try different keywords'}
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    );
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* 搜索输入框 */}
      <div className="relative">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder || t('placeholder') || 'Search for games...'}
          className="w-full pl-12 pr-12 py-3 rounded-full border border-border bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all"
        />
        {query && (
          <button
            onClick={clearSearch}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        )}
        {showFilters && (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="absolute right-12 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <Filter className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* 搜索建议下拉框 */}
      {renderSuggestions()}
    </div>
  );
}
