'use client';

import { useEffect, useState } from 'react';

// Font loading optimization component
export default function FontOptimization() {
  useEffect(() => {
    // Check if font loading API is supported
    if ('fonts' in document) {
      // Preload critical fonts
      const criticalFonts = [
        new FontFace('Inter', 'url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2)', {
          weight: '400',
          style: 'normal',
          display: 'swap'
        }),
        new FontFace('Inter', 'url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZ9hiA.woff2)', {
          weight: '500',
          style: 'normal',
          display: 'swap'
        }),
        new FontFace('Inter', 'url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKfAZ9hiA.woff2)', {
          weight: '600',
          style: 'normal',
          display: 'swap'
        }),
        new FontFace('Inter', 'url(https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hiA.woff2)', {
          weight: '700',
          style: 'normal',
          display: 'swap'
        })
      ];

      // Load fonts asynchronously
      Promise.all(
        criticalFonts.map(font => {
          document.fonts.add(font);
          return font.load();
        })
      ).then(() => {
        console.log('Critical fonts loaded');
        document.documentElement.classList.add('fonts-loaded');
      }).catch(error => {
        console.error('Font loading failed:', error);
      });

      // Monitor font loading
      document.fonts.ready.then(() => {
        console.log('All fonts loaded');
        // Trigger any layout recalculations if needed
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new Event('fontsloaded'));
        }
      });
    }

    // Fallback for browsers without font loading API
    else {
      // Use a timeout to assume fonts are loaded
      setTimeout(() => {
        document.documentElement.classList.add('fonts-loaded');
      }, 3000);
    }
  }, []);

  return null;
}

// Hook for font loading status
export function useFontLoading() {
  const [fontsLoaded, setFontsLoaded] = useState(false);

  useEffect(() => {
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        setFontsLoaded(true);
      });
    } else {
      // Fallback
      setTimeout(() => {
        setFontsLoaded(true);
      }, 3000);
    }
  }, []);

  return fontsLoaded;
}

// Font preloading utility
export const preloadFonts = (fonts: Array<{ url: string; weight: string; style: string }>) => {
  if (typeof window === 'undefined' || !('fonts' in document)) return;

  fonts.forEach(({ url, weight, style }) => {
    const font = new FontFace('Inter', `url(${url})`, {
      weight,
      style,
      display: 'swap'
    });

    document.fonts.add(font);
    font.load().catch(error => {
      console.error('Font preload failed:', error);
    });
  });
};

// Critical font subset for Chinese characters
export const preloadChineseFonts = () => {
  if (typeof window === 'undefined') return;

  const chineseFontSubsets: string[] = [
    // Add Chinese font URLs here if needed
    // For now, we'll rely on system fonts for Chinese
  ];

  chineseFontSubsets.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'font';
    link.type = 'font/woff2';
    link.crossOrigin = 'anonymous';
    link.href = url;
    document.head.appendChild(link);
  });
};
