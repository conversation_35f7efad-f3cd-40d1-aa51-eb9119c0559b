'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { gameDataManager, initializeGameData } from '@/lib/gameDataManager';
import { ExtendedGameData, CategoryIndex, TagIndex } from '@/config/gameDataConfig';
import { performanceMonitor, measureSync } from '@/utils/performanceMonitor';

// Context类型定义
interface GameDataContextType {
  // 数据状态
  initialized: boolean;
  loading: boolean;
  error: string | null;
  
  // 游戏数据
  allGames: ExtendedGameData[];
  categories: CategoryIndex[];
  tags: TagIndex[];
  
  // 统计信息
  stats: {
    totalGames: number;
    categories: number;
    tags: number;
    cacheSize: number;
    initialized: boolean;
  };
  
  // 操作方法
  refreshData: () => Promise<void>;
  clearCache: () => void;
  
  // 快捷查询方法
  getGameById: (id: string) => ExtendedGameData | null;
  getGamesByCategory: (categoryId: string) => ExtendedGameData[];
  getGamesByTag: (tag: string) => ExtendedGameData[];
  searchGames: (query: string, options?: any) => ExtendedGameData[];
  getPopularGames: (limit?: number) => ExtendedGameData[];
  getNewGames: (limit?: number) => ExtendedGameData[];
}

// 创建Context
const GameDataContext = createContext<GameDataContextType | null>(null);

// Provider组件属性
interface GameDataProviderProps {
  children: ReactNode;
  autoInitialize?: boolean;
  enablePerformanceMonitoring?: boolean;
}

// Provider组件
export function GameDataProvider({ 
  children, 
  autoInitialize = true,
  enablePerformanceMonitoring = true 
}: GameDataProviderProps) {
  const [initialized, setInitialized] = useState(false);
  const [loading, setLoading] = useState(autoInitialize);
  const [error, setError] = useState<string | null>(null);
  const [allGames, setAllGames] = useState<ExtendedGameData[]>([]);
  const [categories, setCategories] = useState<CategoryIndex[]>([]);
  const [tags, setTags] = useState<TagIndex[]>([]);
  const [stats, setStats] = useState({
    totalGames: 0,
    categories: 0,
    tags: 0,
    cacheSize: 0,
    initialized: false,
  });

  // 初始化数据
  const initializeData = async () => {
    if (initialized) return;

    try {
      setLoading(true);
      setError(null);

      if (enablePerformanceMonitoring) {
        performanceMonitor.startTiming('game-data-initialization');
      }

      // 初始化游戏数据管理器
      await initializeGameData();

      // 获取所有数据
      const games = gameDataManager.getAllGames();
      const categoryIndex = gameDataManager.getCategoryIndex();
      const tagIndex = gameDataManager.getTagIndex();
      const statsData = gameDataManager.getStats();

      // 更新状态
      setAllGames(games);
      setCategories(categoryIndex);
      setTags(tagIndex);
      setStats(statsData);
      setInitialized(true);

      if (enablePerformanceMonitoring) {
        performanceMonitor.endTiming('game-data-initialization', 'loading', {
          gamesLoaded: games.length,
          categoriesLoaded: categoryIndex.length,
          tagsLoaded: tagIndex.length,
        });
      }

      console.log('Game data initialized successfully:', {
        games: games.length,
        categories: categoryIndex.length,
        tags: tagIndex.length,
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize game data';
      setError(errorMessage);
      console.error('Game data initialization failed:', err);

      if (enablePerformanceMonitoring) {
        performanceMonitor.recordMetric({
          name: 'game-data-initialization-error',
          value: 1,
          category: 'loading',
          details: { error: errorMessage },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const refreshData = async () => {
    setInitialized(false);
    gameDataManager.clearCache();
    await initializeData();
  };

  // 清除缓存
  const clearCache = () => {
    gameDataManager.clearCache();
    setStats(gameDataManager.getStats());
  };

  // 快捷查询方法
  const getGameById = (id: string): ExtendedGameData | null => {
    if (!initialized) return null;
    return gameDataManager.getGameById(id);
  };

  const getGamesByCategory = (categoryId: string): ExtendedGameData[] => {
    if (!initialized) return [];
    return gameDataManager.getGamesByCategory(categoryId);
  };

  const getGamesByTag = (tag: string): ExtendedGameData[] => {
    if (!initialized) return [];
    return gameDataManager.getGamesByTag(tag);
  };

  const searchGames = (query: string, options?: any): ExtendedGameData[] => {
    if (!initialized) return [];
    
    if (enablePerformanceMonitoring) {
      return measureSync(
        'search-games',
        () => gameDataManager.searchGames(query, options),
        'interaction'
      );
    }
    
    return gameDataManager.searchGames(query, options);
  };

  const getPopularGames = (limit?: number): ExtendedGameData[] => {
    if (!initialized) return [];
    return gameDataManager.getPopularGames(limit);
  };

  const getNewGames = (limit?: number): ExtendedGameData[] => {
    if (!initialized) return [];
    return gameDataManager.getNewGames(limit);
  };

  // 自动初始化
  useEffect(() => {
    if (autoInitialize) {
      initializeData();
    }
  }, [autoInitialize]);

  // 定期更新统计信息
  useEffect(() => {
    if (!initialized) return;

    const interval = setInterval(() => {
      setStats(gameDataManager.getStats());
    }, 30000); // 每30秒更新一次

    return () => clearInterval(interval);
  }, [initialized]);

  // Context值
  const contextValue: GameDataContextType = {
    // 数据状态
    initialized,
    loading,
    error,
    
    // 游戏数据
    allGames,
    categories,
    tags,
    
    // 统计信息
    stats,
    
    // 操作方法
    refreshData,
    clearCache,
    
    // 快捷查询方法
    getGameById,
    getGamesByCategory,
    getGamesByTag,
    searchGames,
    getPopularGames,
    getNewGames,
  };

  return (
    <GameDataContext.Provider value={contextValue}>
      {children}
    </GameDataContext.Provider>
  );
}

// Hook for using game data context
export function useGameDataContext(): GameDataContextType {
  const context = useContext(GameDataContext);
  if (!context) {
    throw new Error('useGameDataContext must be used within a GameDataProvider');
  }
  return context;
}

// Hook for checking if data is ready
export function useGameDataReady(): boolean {
  const { initialized, loading, error } = useGameDataContext();
  return initialized && !loading && !error;
}

// Hook for getting loading state
export function useGameDataLoading(): {
  loading: boolean;
  error: string | null;
  initialized: boolean;
} {
  const { loading, error, initialized } = useGameDataContext();
  return { loading, error, initialized };
}

// 预加载组件 - 在应用启动时预加载数据
interface GameDataPreloaderProps {
  children: ReactNode;
  fallback?: ReactNode;
  showProgress?: boolean;
}

export function GameDataPreloader({ 
  children, 
  fallback,
  showProgress = false 
}: GameDataPreloaderProps) {
  const { initialized, loading, error } = useGameDataContext();

  // 显示加载状态
  if (loading && !initialized) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <div className="text-lg font-medium">Loading Game Data...</div>
          {showProgress && (
            <div className="text-sm text-muted-foreground">
              Initializing game database and search indices
            </div>
          )}
        </div>
      </div>
    );
  }

  // 显示错误状态
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-red-500 text-6xl">⚠️</div>
          <div className="text-lg font-medium">Failed to Load Game Data</div>
          <div className="text-sm text-muted-foreground">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // 数据已加载，渲染子组件
  return <>{children}</>;
}

export default GameDataProvider;
