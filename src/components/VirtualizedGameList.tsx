'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExtendedGameData } from '@/config/gameDataConfig';
import { gameDataConfig } from '@/config/gameDataConfig';
import GameCard from './GameCard';
import { useMobileDetection } from './MobileDetection';

interface VirtualizedGameListProps {
  games: ExtendedGameData[];
  loading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  locale?: string;
  className?: string;
  itemHeight?: number;
  overscan?: number;
  enableVirtualization?: boolean;
}

interface VirtualItem {
  index: number;
  game: ExtendedGameData;
  top: number;
  height: number;
}

export default function VirtualizedGameList({
  games,
  loading = false,
  hasMore = false,
  onLoadMore,
  locale = 'en',
  className = '',
  itemHeight = 300,
  overscan = 5,
  enableVirtualization = true,
}: VirtualizedGameListProps) {
  const { isMobile, isTablet } = useMobileDetection();
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isIntersecting, setIsIntersecting] = useState(false);

  // 根据设备类型调整列数
  const columns = useMemo(() => {
    if (isMobile) return 1;
    if (isTablet) return 2;
    return 4;
  }, [isMobile, isTablet]);

  // 计算行数
  const rows = Math.ceil(games.length / columns);
  const totalHeight = rows * itemHeight;

  // 是否启用虚拟化
  const shouldVirtualize = enableVirtualization && 
    games.length > gameDataConfig.performance.virtualizationThreshold;

  // 计算可见范围
  const visibleRange = useMemo(() => {
    if (!shouldVirtualize) {
      return { start: 0, end: games.length };
    }

    const startRow = Math.floor(scrollTop / itemHeight);
    const endRow = Math.min(
      rows,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );

    const start = Math.max(0, (startRow - overscan) * columns);
    const end = Math.min(games.length, (endRow + overscan) * columns);

    return { start, end };
  }, [scrollTop, containerHeight, itemHeight, rows, columns, overscan, shouldVirtualize, games.length]);

  // 可见的游戏项
  const visibleItems = useMemo((): VirtualItem[] => {
    const items: VirtualItem[] = [];
    
    for (let i = visibleRange.start; i < visibleRange.end; i++) {
      if (i >= games.length) break;
      
      const row = Math.floor(i / columns);
      const top = row * itemHeight;
      
      items.push({
        index: i,
        game: games[i],
        top,
        height: itemHeight,
      });
    }
    
    return items;
  }, [visibleRange, games, columns, itemHeight]);

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    setScrollTop(target.scrollTop);

    // 检查是否需要加载更多
    if (hasMore && onLoadMore && !loading) {
      const { scrollTop, scrollHeight, clientHeight } = target;
      const threshold = gameDataConfig.lazyLoading.loadTriggerDistance;
      
      if (scrollHeight - scrollTop - clientHeight < threshold) {
        onLoadMore();
      }
    }
  }, [hasMore, onLoadMore, loading]);

  // 监听容器尺寸变化
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!containerRef.current || !onLoadMore) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsIntersecting(entry.isIntersecting);
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(containerRef.current);
    return () => observer.disconnect();
  }, [onLoadMore]);

  // 渲染虚拟化列表
  const renderVirtualizedList = () => (
    <div
      ref={containerRef}
      className={`relative overflow-auto ${className}`}
      style={{ height: '100%' }}
      onScroll={handleScroll}
    >
      {/* 总高度占位符 */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* 可见项目 */}
        <AnimatePresence mode="popLayout">
          {visibleItems.map(({ index, game, top }) => (
            <motion.div
              key={game.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: (index % columns) * 0.05 }}
              style={{
                position: 'absolute',
                top,
                left: `${(index % columns) * (100 / columns)}%`,
                width: `${100 / columns}%`,
                height: itemHeight,
                padding: '8px',
              }}
            >
              <GameCard 
                game={game} 
                locale={locale} 
                priority={index < gameDataConfig.lazyLoading.preloadImageCount}
                className="h-full"
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* 加载更多指示器 */}
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading more games...</span>
        </div>
      )}
    </div>
  );

  // 渲染普通网格
  const renderNormalGrid = () => (
    <div
      ref={containerRef}
      className={`grid gap-6 ${className}`}
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
      }}
      onScroll={handleScroll}
    >
      <AnimatePresence mode="popLayout">
        {games.map((game, index) => (
          <motion.div
            key={game.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, delay: index * 0.02 }}
          >
            <GameCard 
              game={game} 
              locale={locale} 
              priority={index < gameDataConfig.lazyLoading.preloadImageCount}
            />
          </motion.div>
        ))}
      </AnimatePresence>

      {/* 加载更多指示器 */}
      {loading && (
        <div className="col-span-full flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading more games...</span>
        </div>
      )}
    </div>
  );

  return shouldVirtualize ? renderVirtualizedList() : renderNormalGrid();
}

// 无限滚动组件
interface InfiniteScrollGameListProps extends Omit<VirtualizedGameListProps, 'onLoadMore'> {
  onLoadMore: () => Promise<void> | void;
  threshold?: number;
}

export function InfiniteScrollGameList({
  games,
  loading = false,
  hasMore = false,
  onLoadMore,
  threshold = 500,
  ...props
}: InfiniteScrollGameListProps) {
  const [isLoading, setIsLoading] = useState(false);
  const loadingRef = useRef(false);

  const handleLoadMore = useCallback(async () => {
    if (loadingRef.current || !hasMore) return;
    
    loadingRef.current = true;
    setIsLoading(true);
    
    try {
      await onLoadMore();
    } catch (error) {
      console.error('Failed to load more games:', error);
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [hasMore, onLoadMore]);

  return (
    <VirtualizedGameList
      {...props}
      games={games}
      loading={loading || isLoading}
      hasMore={hasMore}
      onLoadMore={handleLoadMore}
    />
  );
}

// 分页游戏列表组件
interface PaginatedGameListProps extends Omit<VirtualizedGameListProps, 'hasMore' | 'onLoadMore'> {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showPagination?: boolean;
}

export function PaginatedGameList({
  games,
  currentPage,
  totalPages,
  onPageChange,
  showPagination = true,
  ...props
}: PaginatedGameListProps) {
  const renderPagination = () => {
    if (!showPagination || totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => onPageChange(i)}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            i === currentPage
              ? 'bg-primary text-primary-foreground'
              : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
          }`}
        >
          {i}
        </button>
      );
    }

    return (
      <div className="flex justify-center items-center space-x-2 mt-8">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="px-3 py-2 rounded-md text-sm font-medium bg-secondary text-secondary-foreground hover:bg-secondary/80 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        {pages}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="px-3 py-2 rounded-md text-sm font-medium bg-secondary text-secondary-foreground hover:bg-secondary/80 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
    );
  };

  return (
    <div>
      <VirtualizedGameList
        {...props}
        games={games}
        enableVirtualization={false} // 分页模式下禁用虚拟化
      />
      {renderPagination()}
    </div>
  );
}
