import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-4">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

// Lazy load heavy components to reduce initial bundle size
export const LazyGameTags = dynamic(() => import('./GameTags'), {
  loading: () => <LoadingSpinner />,
  ssr: false,
});

export const LazyOptimizedImage = dynamic(() => import('./OptimizedImage'), {
  loading: () => <div className="bg-secondary animate-pulse aspect-video rounded-lg" />,
  ssr: false,
});

export const LazyBreadcrumb = dynamic(() => import('./Breadcrumb'), {
  loading: () => <div className="h-6 bg-secondary animate-pulse rounded w-48" />,
  ssr: true,
});

// Lazy load search page components
export const LazySearchPage = dynamic(() => import('../app/[locale]/search/page'), {
  loading: () => (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-secondary rounded w-1/3"></div>
          <div className="h-4 bg-secondary rounded w-2/3"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="aspect-video bg-secondary rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  ),
  ssr: false,
});

// Lazy load tag pages
export const LazyTagsPage = dynamic(() => import('../app/[locale]/tags/page'), {
  loading: () => (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary rounded w-1/4"></div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {Array.from({ length: 12 }).map((_, i) => (
              <div key={i} className="h-16 bg-secondary rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  ),
  ssr: false,
});

export const LazyTagPage = dynamic(() => import('../app/[locale]/tags/[tag]/page'), {
  loading: () => (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary rounded w-1/3"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="aspect-video bg-secondary rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  ),
  ssr: false,
});

// Lazy load motion components to reduce initial bundle
export const LazyMotionDiv = dynamic(
  () => import('framer-motion').then((mod) => mod.motion.div as ComponentType<any>),
  {
    loading: () => <div />,
    ssr: false,
  }
);

// Lazy load icons to reduce bundle size
export const LazyIcons = {
  Search: dynamic(() => import('lucide-react').then((mod) => ({ default: mod.Search })), {
    loading: () => <div className="w-5 h-5 bg-secondary rounded animate-pulse" />,
    ssr: false,
  }),
  Tag: dynamic(() => import('lucide-react').then((mod) => ({ default: mod.Tag })), {
    loading: () => <div className="w-5 h-5 bg-secondary rounded animate-pulse" />,
    ssr: false,
  }),
  Filter: dynamic(() => import('lucide-react').then((mod) => ({ default: mod.Filter })), {
    loading: () => <div className="w-5 h-5 bg-secondary rounded animate-pulse" />,
    ssr: false,
  }),
  Grid: dynamic(() => import('lucide-react').then((mod) => ({ default: mod.Grid })), {
    loading: () => <div className="w-5 h-5 bg-secondary rounded animate-pulse" />,
    ssr: false,
  }),
  List: dynamic(() => import('lucide-react').then((mod) => ({ default: mod.List })), {
    loading: () => <div className="w-5 h-5 bg-secondary rounded animate-pulse" />,
    ssr: false,
  }),
};

// Preload critical components
export const preloadCriticalComponents = () => {
  if (typeof window !== 'undefined') {
    // Preload components that are likely to be used
    import('./GameTags');
    import('./OptimizedImage');
    import('./Breadcrumb');
  }
};

// Bundle analyzer helper
export const getBundleInfo = () => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    console.log('Bundle analysis available in development mode');
    // You can add bundle analysis logic here
  }
};
