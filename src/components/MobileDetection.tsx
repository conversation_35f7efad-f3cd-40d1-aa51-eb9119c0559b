'use client';

import { useState, useEffect } from 'react';

// Mobile device detection hook
export function useMobileDetection() {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const checkDevice = () => {
      if (typeof window === 'undefined') return;

      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      setOrientation(width > height ? 'landscape' : 'portrait');

      // Mobile detection based on screen size and user agent
      const userAgent = navigator.userAgent.toLowerCase();
      const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
      const isMobileUA = mobileRegex.test(userAgent);
      
      // Screen size based detection
      const isMobileSize = width <= 768;
      const isTabletSize = width > 768 && width <= 1024;
      
      const mobile = isMobileUA || isMobileSize;
      const tablet = !mobile && (isTabletSize || /ipad/i.test(userAgent));
      
      setIsMobile(mobile);
      setIsTablet(tablet);
      
      if (mobile) {
        setDeviceType('mobile');
      } else if (tablet) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    window.addEventListener('orientationchange', checkDevice);

    return () => {
      window.removeEventListener('resize', checkDevice);
      window.removeEventListener('orientationchange', checkDevice);
    };
  }, []);

  return {
    isMobile,
    isTablet,
    deviceType,
    orientation,
    screenSize,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape',
  };
}

// Mobile-friendly game wrapper component
interface MobileGameWrapperProps {
  gameUrl: string;
  title: string;
  children?: React.ReactNode;
  onMobileWarning?: () => void;
}

export function MobileGameWrapper({ 
  gameUrl, 
  title, 
  children, 
  onMobileWarning 
}: MobileGameWrapperProps) {
  const { isMobile, isTablet, deviceType, orientation } = useMobileDetection();
  const [showMobileWarning, setShowMobileWarning] = useState(false);

  useEffect(() => {
    // Check if game is mobile-friendly
    const isMobileFriendly = checkGameMobileCompatibility(gameUrl);
    
    if ((isMobile || isTablet) && !isMobileFriendly) {
      setShowMobileWarning(true);
      onMobileWarning?.();
    }
  }, [gameUrl, isMobile, isTablet, onMobileWarning]);

  if (showMobileWarning) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Mobile Compatibility Notice
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                This game may not be fully optimized for {deviceType} devices. 
                For the best experience, we recommend playing on a desktop computer.
              </p>
              {orientation === 'portrait' && (
                <p className="mt-1">
                  Try rotating your device to landscape mode for better gameplay.
                </p>
              )}
            </div>
            <div className="mt-4">
              <div className="flex">
                <button
                  onClick={() => setShowMobileWarning(false)}
                  className="bg-yellow-100 px-2 py-1 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  Continue Anyway
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`mobile-game-wrapper ${deviceType}`}>
      {children}
    </div>
  );
}

// Check if a game is mobile-friendly
function checkGameMobileCompatibility(gameUrl: string): boolean {
  // List of mobile-friendly game indicators
  const mobileFriendlyIndicators = [
    'html5',
    'mobile',
    'touch',
    'responsive',
    'webgl',
  ];

  // List of games known to work well on mobile
  const mobileFriendlyGames = [
    'snow-rider-3d',
    'crazy-strike-force',
    // Add more mobile-friendly game IDs here
  ];

  // Extract game ID from URL or check URL patterns
  const gameId = extractGameIdFromUrl(gameUrl);
  
  if (mobileFriendlyGames.includes(gameId)) {
    return true;
  }

  // Check URL for mobile-friendly indicators
  const urlLower = gameUrl.toLowerCase();
  return mobileFriendlyIndicators.some(indicator => 
    urlLower.includes(indicator)
  );
}

// Extract game ID from URL
function extractGameIdFromUrl(url: string): string {
  // Extract game ID from various URL patterns
  const patterns = [
    /\/games\/([^\/]+)/,
    /\/game\/([^\/]+)/,
    /\/([^\/]+)\.html/,
    /\/([^\/]+)$/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }

  return '';
}

// Mobile optimization utilities
export const mobileUtils = {
  // Prevent zoom on input focus (iOS)
  preventZoomOnFocus: () => {
    if (typeof document !== 'undefined') {
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
        );
      }
    }
  },

  // Enable zoom back
  enableZoom: () => {
    if (typeof document !== 'undefined') {
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0'
        );
      }
    }
  },

  // Check if device supports touch
  isTouchDevice: () => {
    return typeof window !== 'undefined' && (
      'ontouchstart' in window ||
      navigator.maxTouchPoints > 0 ||
      (navigator as any).msMaxTouchPoints > 0
    );
  },

  // Get device pixel ratio
  getDevicePixelRatio: () => {
    return typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
  },

  // Check if device is in standalone mode (PWA)
  isStandalone: () => {
    return typeof window !== 'undefined' && (
      window.matchMedia('(display-mode: standalone)').matches ||
      (window.navigator as any).standalone === true
    );
  },
};

// Mobile-specific CSS classes
export const mobileClasses = {
  hideOnMobile: 'hidden md:block',
  showOnMobile: 'block md:hidden',
  mobileOnly: 'md:hidden',
  tabletUp: 'hidden md:block',
  mobileAndTablet: 'lg:hidden',
  touchOptimized: 'touch-manipulation select-none',
  mobileSpacing: 'px-4 md:px-6 lg:px-8',
  mobileText: 'text-sm md:text-base',
  mobilePadding: 'p-2 md:p-4',
};
