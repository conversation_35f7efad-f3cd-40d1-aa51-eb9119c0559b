'use client';

import { useMobileDetection } from './MobileDetection';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  minItemWidth?: number;
  gap?: number;
  mobileColumns?: number;
  tabletColumns?: number;
  desktopColumns?: number;
}

export default function ResponsiveGrid({
  children,
  className = '',
  minItemWidth = 280,
  gap = 16,
  mobileColumns = 1,
  tabletColumns = 2,
  desktopColumns = 4,
}: ResponsiveGridProps) {
  const { isMobile, isTablet } = useMobileDetection();

  // Determine grid columns based on device type
  const getGridColumns = () => {
    if (isMobile) return mobileColumns;
    if (isTablet) return tabletColumns;
    return desktopColumns;
  };

  const gridColumns = getGridColumns();

  return (
    <div
      className={`grid gap-${gap / 4} ${className}`}
      style={{
        gridTemplateColumns: `repeat(${gridColumns}, minmax(${minItemWidth}px, 1fr))`,
        gap: `${gap}px`,
      }}
    >
      {children}
    </div>
  );
}

// Responsive container component
interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export function ResponsiveContainer({
  children,
  className = '',
  maxWidth = 'xl',
  padding = 'md',
}: ResponsiveContainerProps) {
  const { isMobile, isTablet } = useMobileDetection();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-7xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: isMobile ? 'px-2' : 'px-4',
    md: isMobile ? 'px-4' : isTablet ? 'px-6' : 'px-8',
    lg: isMobile ? 'px-6' : isTablet ? 'px-8' : 'px-12',
  };

  return (
    <div className={`mx-auto ${maxWidthClasses[maxWidth]} ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
}

// Responsive text component
interface ResponsiveTextProps {
  children: React.ReactNode;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl';
  className?: string;
  responsive?: boolean;
}

export function ResponsiveText({
  children,
  as: Component = 'p',
  size = 'base',
  className = '',
  responsive = true,
}: ResponsiveTextProps) {
  const { isMobile, isTablet } = useMobileDetection();

  const getResponsiveSize = () => {
    if (!responsive) return size;

    const sizeMap = {
      xs: isMobile ? 'text-xs' : 'text-xs',
      sm: isMobile ? 'text-xs' : 'text-sm',
      base: isMobile ? 'text-sm' : isTablet ? 'text-base' : 'text-base',
      lg: isMobile ? 'text-base' : isTablet ? 'text-lg' : 'text-lg',
      xl: isMobile ? 'text-lg' : isTablet ? 'text-xl' : 'text-xl',
      '2xl': isMobile ? 'text-xl' : isTablet ? 'text-2xl' : 'text-2xl',
      '3xl': isMobile ? 'text-2xl' : isTablet ? 'text-3xl' : 'text-3xl',
      '4xl': isMobile ? 'text-3xl' : isTablet ? 'text-4xl' : 'text-4xl',
      '5xl': isMobile ? 'text-4xl' : isTablet ? 'text-5xl' : 'text-5xl',
      '6xl': isMobile ? 'text-5xl' : isTablet ? 'text-6xl' : 'text-6xl',
    };

    return sizeMap[size] || 'text-base';
  };

  const responsiveSize = getResponsiveSize();

  return (
    <Component className={`${responsiveSize} ${className}`}>
      {children}
    </Component>
  );
}

// Responsive spacing component
interface ResponsiveSpacingProps {
  children: React.ReactNode;
  className?: string;
  vertical?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  horizontal?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export function ResponsiveSpacing({
  children,
  className = '',
  vertical = 'md',
  horizontal = 'md',
}: ResponsiveSpacingProps) {
  const { isMobile, isTablet } = useMobileDetection();

  const getSpacing = (size: string, direction: 'vertical' | 'horizontal') => {
    const spacingMap = {
      xs: {
        vertical: isMobile ? 'py-1' : 'py-2',
        horizontal: isMobile ? 'px-2' : 'px-3',
      },
      sm: {
        vertical: isMobile ? 'py-2' : 'py-3',
        horizontal: isMobile ? 'px-3' : 'px-4',
      },
      md: {
        vertical: isMobile ? 'py-3' : isTablet ? 'py-4' : 'py-6',
        horizontal: isMobile ? 'px-4' : isTablet ? 'px-6' : 'px-8',
      },
      lg: {
        vertical: isMobile ? 'py-4' : isTablet ? 'py-6' : 'py-8',
        horizontal: isMobile ? 'px-6' : isTablet ? 'px-8' : 'px-12',
      },
      xl: {
        vertical: isMobile ? 'py-6' : isTablet ? 'py-8' : 'py-12',
        horizontal: isMobile ? 'px-8' : isTablet ? 'px-12' : 'px-16',
      },
    };

    return spacingMap[size as keyof typeof spacingMap]?.[direction] || '';
  };

  const verticalSpacing = getSpacing(vertical, 'vertical');
  const horizontalSpacing = getSpacing(horizontal, 'horizontal');

  return (
    <div className={`${verticalSpacing} ${horizontalSpacing} ${className}`}>
      {children}
    </div>
  );
}

// Responsive image aspect ratio component
interface ResponsiveAspectRatioProps {
  children: React.ReactNode;
  ratio?: 'square' | 'video' | 'portrait' | 'landscape';
  mobileRatio?: 'square' | 'video' | 'portrait' | 'landscape';
  className?: string;
}

export function ResponsiveAspectRatio({
  children,
  ratio = 'video',
  mobileRatio,
  className = '',
}: ResponsiveAspectRatioProps) {
  const { isMobile } = useMobileDetection();

  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]',
  };

  const currentRatio = isMobile && mobileRatio ? mobileRatio : ratio;

  return (
    <div className={`${aspectRatioClasses[currentRatio]} ${className}`}>
      {children}
    </div>
  );
}

// Responsive flex component
interface ResponsiveFlexProps {
  children: React.ReactNode;
  direction?: 'row' | 'col';
  mobileDirection?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function ResponsiveFlex({
  children,
  direction = 'row',
  mobileDirection,
  align = 'start',
  justify = 'start',
  gap = 'md',
  className = '',
}: ResponsiveFlexProps) {
  const { isMobile } = useMobileDetection();

  const currentDirection = isMobile && mobileDirection ? mobileDirection : direction;

  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    xs: 'gap-1',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  return (
    <div
      className={`flex ${directionClasses[currentDirection]} ${alignClasses[align]} ${justifyClasses[justify]} ${gapClasses[gap]} ${className}`}
    >
      {children}
    </div>
  );
}
