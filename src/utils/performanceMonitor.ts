'use client';

import { gameDataConfig } from '@/config/gameDataConfig';

// 性能指标类型
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  category: 'loading' | 'rendering' | 'interaction' | 'memory' | 'network';
  details?: Record<string, any>;
}

// 性能监控器
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private startTimes: Map<string, number> = new Map();
  private enabled: boolean;

  constructor() {
    this.enabled = gameDataConfig.analytics.performanceMonitoring;
    if (this.enabled && typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  // 初始化性能观察器
  private initializeObservers(): void {
    try {
      // 观察导航性能
      if ('PerformanceObserver' in window) {
        const navObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'navigation') {
              this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
            }
          });
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // 观察资源加载性能
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'resource') {
              this.recordResourceMetric(entry as PerformanceResourceTiming);
            }
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);

        // 观察长任务
        const longTaskObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            this.recordMetric({
              name: 'long-task',
              value: entry.duration,
              category: 'rendering',
              details: {
                startTime: entry.startTime,
                attribution: (entry as any).attribution,
              },
            });
          });
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      }

      // 观察内存使用情况
      this.monitorMemoryUsage();
    } catch (error) {
      console.warn('Failed to initialize performance observers:', error);
    }
  }

  // 记录导航性能指标
  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = [
      { name: 'dns-lookup', value: entry.domainLookupEnd - entry.domainLookupStart },
      { name: 'tcp-connect', value: entry.connectEnd - entry.connectStart },
      { name: 'request-response', value: entry.responseEnd - entry.requestStart },
      { name: 'dom-loading', value: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart },
      { name: 'page-load', value: entry.loadEventEnd - entry.loadEventStart },
      { name: 'total-load-time', value: entry.loadEventEnd - entry.startTime },
    ];

    metrics.forEach(metric => {
      if (metric.value > 0) {
        this.recordMetric({
          name: metric.name,
          value: metric.value,
          category: 'loading',
        });
      }
    });
  }

  // 记录资源加载性能
  private recordResourceMetric(entry: PerformanceResourceTiming): void {
    // 只监控图片和关键资源
    if (entry.name.includes('.jpg') || entry.name.includes('.png') || 
        entry.name.includes('.webp') || entry.name.includes('.avif') ||
        entry.name.includes('api/')) {
      
      this.recordMetric({
        name: 'resource-load',
        value: entry.responseEnd - entry.startTime,
        category: 'network',
        details: {
          url: entry.name,
          size: entry.transferSize,
          type: this.getResourceType(entry.name),
          cached: entry.transferSize === 0,
        },
      });
    }
  }

  // 获取资源类型
  private getResourceType(url: string): string {
    if (url.includes('.jpg') || url.includes('.png') || url.includes('.webp') || url.includes('.avif')) {
      return 'image';
    }
    if (url.includes('api/')) {
      return 'api';
    }
    return 'other';
  }

  // 监控内存使用情况
  private monitorMemoryUsage(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.recordMetric({
          name: 'memory-usage',
          value: memory.usedJSHeapSize,
          category: 'memory',
          details: {
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
            percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
          },
        });
      }, 30000); // 每30秒记录一次
    }
  }

  // 记录性能指标
  recordMetric(metric: Omit<PerformanceMetric, 'timestamp'>): void {
    if (!this.enabled) return;

    const fullMetric: PerformanceMetric = {
      ...metric,
      timestamp: Date.now(),
    };

    this.metrics.push(fullMetric);

    // 限制指标数量
    if (this.metrics.length > (gameDataConfig.analytics.performanceMonitoring ? 1000 : 0)) {
      this.metrics = this.metrics.slice(-500);
    }

    // 检查是否有性能问题
    this.checkPerformanceIssues(fullMetric);
  }

  // 开始计时
  startTiming(name: string): void {
    if (!this.enabled) return;
    this.startTimes.set(name, performance.now());
  }

  // 结束计时
  endTiming(name: string, category: PerformanceMetric['category'] = 'interaction', details?: Record<string, any>): void {
    if (!this.enabled) return;
    
    const startTime = this.startTimes.get(name);
    if (startTime !== undefined) {
      const duration = performance.now() - startTime;
      this.recordMetric({
        name,
        value: duration,
        category,
        details,
      });
      this.startTimes.delete(name);
    }
  }

  // 检查性能问题
  private checkPerformanceIssues(metric: PerformanceMetric): void {
    const thresholds = {
      'resource-load': 2000, // 2秒
      'search-time': 500, // 500ms
      'render-time': 100, // 100ms
      'memory-usage': 50 * 1024 * 1024, // 50MB
    };

    const threshold = thresholds[metric.name as keyof typeof thresholds];
    if (threshold && metric.value > threshold) {
      console.warn(`Performance issue detected: ${metric.name} took ${metric.value}ms/bytes`, metric);
      
      // 可以在这里发送警报或记录到分析服务
      if (gameDataConfig.analytics.errorReporting) {
        this.reportPerformanceIssue(metric);
      }
    }
  }

  // 报告性能问题
  private reportPerformanceIssue(metric: PerformanceMetric): void {
    // 这里可以集成第三方分析服务
    console.log('Reporting performance issue:', metric);
  }

  // 获取性能统计
  getStats(): {
    averageLoadTime: number;
    slowestResources: PerformanceMetric[];
    memoryUsage: PerformanceMetric | null;
    totalMetrics: number;
  } {
    const loadMetrics = this.metrics.filter(m => m.category === 'loading');
    const resourceMetrics = this.metrics.filter(m => m.name === 'resource-load');
    const memoryMetrics = this.metrics.filter(m => m.name === 'memory-usage');

    return {
      averageLoadTime: loadMetrics.length > 0 
        ? loadMetrics.reduce((sum, m) => sum + m.value, 0) / loadMetrics.length 
        : 0,
      slowestResources: resourceMetrics
        .sort((a, b) => b.value - a.value)
        .slice(0, 5),
      memoryUsage: memoryMetrics.length > 0 ? memoryMetrics[memoryMetrics.length - 1] : null,
      totalMetrics: this.metrics.length,
    };
  }

  // 获取所有指标
  getAllMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // 清除指标
  clearMetrics(): void {
    this.metrics = [];
    this.startTimes.clear();
  }

  // 销毁监控器
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.clearMetrics();
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 便捷函数
export function measureAsync<T>(
  name: string, 
  fn: () => Promise<T>, 
  category: PerformanceMetric['category'] = 'interaction'
): Promise<T> {
  performanceMonitor.startTiming(name);
  return fn().finally(() => {
    performanceMonitor.endTiming(name, category);
  });
}

export function measureSync<T>(
  name: string, 
  fn: () => T, 
  category: PerformanceMetric['category'] = 'interaction'
): T {
  performanceMonitor.startTiming(name);
  try {
    return fn();
  } finally {
    performanceMonitor.endTiming(name, category);
  }
}

// React Hook for performance monitoring
export function usePerformanceMonitor() {
  return {
    recordMetric: (metric: Omit<PerformanceMetric, 'timestamp'>) => 
      performanceMonitor.recordMetric(metric),
    startTiming: (name: string) => performanceMonitor.startTiming(name),
    endTiming: (name: string, category?: PerformanceMetric['category'], details?: Record<string, any>) => 
      performanceMonitor.endTiming(name, category, details),
    getStats: () => performanceMonitor.getStats(),
    measureAsync,
    measureSync,
  };
}

export default performanceMonitor;
