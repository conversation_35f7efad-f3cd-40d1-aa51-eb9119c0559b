// 图片优化测试工具
export class ImageOptimizationTester {
  private testResults: Array<{
    url: string;
    originalLoadTime: number;
    optimizedLoadTime: number;
    improvement: number;
    cacheHit: boolean;
  }> = [];

  // 测试单个图片的加载性能
  async testImageLoad(originalUrl: string, useProxy: boolean = true): Promise<{
    loadTime: number;
    fromCache: boolean;
    size: number;
  }> {
    const startTime = performance.now();
    
    try {
      const testUrl = useProxy ? this.getProxyUrl(originalUrl) : originalUrl;
      
      const response = await fetch(testUrl);
      const blob = await response.blob();
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // 检查是否来自缓存
      const fromCache = response.headers.get('cf-cache-status') === 'HIT' || 
                       response.headers.get('x-cache') === 'HIT' ||
                       loadTime < 50; // 如果加载时间很短，可能来自缓存

      return {
        loadTime,
        fromCache,
        size: blob.size
      };
    } catch (error) {
      console.error('Image load test failed:', error);
      return {
        loadTime: -1,
        fromCache: false,
        size: 0
      };
    }
  }

  // 批量测试图片加载性能
  async testBatchImageLoad(urls: string[]): Promise<void> {
    console.log('🧪 Starting image optimization test...');
    
    for (const url of urls.slice(0, 5)) { // 只测试前5张图片
      console.log(`Testing: ${url.split('/').pop()}`);
      
      // 测试原始URL
      const originalResult = await this.testImageLoad(url, false);
      await this.delay(100); // 短暂延迟
      
      // 测试优化后的URL
      const optimizedResult = await this.testImageLoad(url, true);
      
      if (originalResult.loadTime > 0 && optimizedResult.loadTime > 0) {
        const improvement = ((originalResult.loadTime - optimizedResult.loadTime) / originalResult.loadTime) * 100;
        
        this.testResults.push({
          url,
          originalLoadTime: originalResult.loadTime,
          optimizedLoadTime: optimizedResult.loadTime,
          improvement,
          cacheHit: optimizedResult.fromCache
        });

        console.log(`  Original: ${originalResult.loadTime.toFixed(2)}ms`);
        console.log(`  Optimized: ${optimizedResult.loadTime.toFixed(2)}ms`);
        console.log(`  Improvement: ${improvement.toFixed(1)}%`);
        console.log(`  Cache Hit: ${optimizedResult.fromCache ? 'Yes' : 'No'}`);
        console.log('---');
      }
    }
    
    this.printSummary();
  }

  // 打印测试总结
  private printSummary(): void {
    if (this.testResults.length === 0) {
      console.log('❌ No test results available');
      return;
    }

    const avgImprovement = this.testResults.reduce((sum, result) => sum + result.improvement, 0) / this.testResults.length;
    const cacheHitRate = (this.testResults.filter(result => result.cacheHit).length / this.testResults.length) * 100;
    const avgOriginalTime = this.testResults.reduce((sum, result) => sum + result.originalLoadTime, 0) / this.testResults.length;
    const avgOptimizedTime = this.testResults.reduce((sum, result) => sum + result.optimizedLoadTime, 0) / this.testResults.length;

    console.log('📊 Image Optimization Test Summary:');
    console.log(`  Tests Run: ${this.testResults.length}`);
    console.log(`  Average Original Load Time: ${avgOriginalTime.toFixed(2)}ms`);
    console.log(`  Average Optimized Load Time: ${avgOptimizedTime.toFixed(2)}ms`);
    console.log(`  Average Improvement: ${avgImprovement.toFixed(1)}%`);
    console.log(`  Cache Hit Rate: ${cacheHitRate.toFixed(1)}%`);
    
    if (avgImprovement > 0) {
      console.log('✅ Optimization is working!');
    } else {
      console.log('⚠️ Optimization may need adjustment');
    }
  }

  // 生成代理URL
  private getProxyUrl(originalUrl: string): string {
    const params = new URLSearchParams({
      url: originalUrl,
      q: '85'
    });
    return `/api/image-proxy?${params.toString()}`;
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 清除测试结果
  clearResults(): void {
    this.testResults = [];
  }

  // 获取测试结果
  getResults(): typeof this.testResults {
    return [...this.testResults];
  }
}

// 在浏览器控制台中运行测试的便捷函数
export function runImageOptimizationTest(): void {
  if (typeof window === 'undefined') {
    console.log('This test can only be run in the browser');
    return;
  }

  // 测试图片URL列表
  const testUrls = [
    'https://www.onlinegames.io/media/posts/898/Deadshot-io.jpg',
    'https://www.onlinegames.io/media/posts/446/Snow-Rider-3D.jpg',
    'https://www.onlinegames.io/media/posts/366/Crazy-Strike-Force.jpg',
    'https://www.onlinegames.io/media/posts/697/Get-on-Top.jpg',
    'https://www.onlinegames.io/media/posts/123/example-game.jpg'
  ];

  const tester = new ImageOptimizationTester();
  tester.testBatchImageLoad(testUrls);
}

// 将测试函数添加到全局对象（仅在开发环境）
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).runImageOptimizationTest = runImageOptimizationTest;
  console.log('🧪 Image optimization test available. Run: runImageOptimizationTest()');
}
