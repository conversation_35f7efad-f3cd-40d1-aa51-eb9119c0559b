// 游戏数据配置和优化策略
export const gameDataConfig = {
  // 缓存策略
  cache: {
    // 启用客户端缓存
    enabled: true,
    // 缓存版本，更新数据时递增
    version: 'v1.0.0',
    // 缓存键前缀
    keyPrefix: 'freehubgames_',
    // 缓存过期时间（毫秒）
    ttl: 24 * 60 * 60 * 1000, // 24小时
    // 最大缓存条目数
    maxEntries: 1000,
  },

  // 搜索索引配置
  search: {
    // 启用客户端搜索索引
    indexEnabled: true,
    // 索引字段权重
    fieldWeights: {
      title: 10,
      category: 5,
      tags: 3,
      description: 2,
    },
    // 最小搜索长度
    minSearchLength: 2,
    // 最大搜索结果数
    maxResults: 50,
    // 模糊搜索阈值
    fuzzyThreshold: 0.6,
  },

  // 分页配置
  pagination: {
    // 默认每页数量
    defaultPageSize: 20,
    // 移动端每页数量
    mobilePageSize: 12,
    // 预加载页数
    preloadPages: 1,
    // 虚拟滚动阈值
    virtualScrollThreshold: 100,
  },

  // 懒加载配置
  lazyLoading: {
    // 启用懒加载
    enabled: true,
    // 初始加载数量
    initialLoadCount: 20,
    // 每次加载数量
    loadMoreCount: 20,
    // 触发加载的距离（像素）
    loadTriggerDistance: 500,
    // 预加载图片数量
    preloadImageCount: 5,
  },

  // 性能优化
  performance: {
    // 启用虚拟化
    virtualizationEnabled: true,
    // 虚拟化阈值
    virtualizationThreshold: 50,
    // 防抖延迟（毫秒）
    debounceDelay: 300,
    // 节流延迟（毫秒）
    throttleDelay: 100,
    // 启用 Web Workers
    webWorkersEnabled: true,
  },

  // 数据预处理
  preprocessing: {
    // 启用数据预处理
    enabled: true,
    // 生成搜索索引
    generateSearchIndex: true,
    // 生成分类索引
    generateCategoryIndex: true,
    // 生成标签索引
    generateTagIndex: true,
    // 预计算热门游戏
    precomputePopular: true,
  },

  // 国际化优化
  i18n: {
    // 预加载语言
    preloadLocales: ['en', 'zh'],
    // 回退语言
    fallbackLocale: 'en',
    // 缓存翻译
    cacheTranslations: true,
  },

  // 图片优化
  images: {
    // 启用图片优化
    enabled: true,
    // 预加载策略
    preloadStrategy: 'smart', // 'eager' | 'lazy' | 'smart'
    // 压缩质量
    quality: 85,
    // 响应式尺寸
    responsiveSizes: {
      mobile: { width: 320, height: 180 },
      tablet: { width: 400, height: 225 },
      desktop: { width: 480, height: 270 },
    },
  },

  // 分析和监控
  analytics: {
    // 启用性能监控
    performanceMonitoring: process.env.NODE_ENV === 'development',
    // 启用用户行为分析
    userBehaviorTracking: false,
    // 错误报告
    errorReporting: true,
  },
};

// 游戏数据类型扩展
export interface ExtendedGameData {
  // 基础数据
  id: string;
  title: { [key: string]: string };
  image: string;
  category: { [key: string]: string };
  rating: number;
  gameUrl: string;
  tags?: string[];
  
  // 扩展数据
  searchIndex?: string; // 预生成的搜索索引
  popularity?: number; // 热门度分数
  addedDate?: Date; // 添加日期
  lastUpdated?: Date; // 最后更新日期
  playCount?: number; // 播放次数（模拟）
  categoryId?: string; // 分类ID
  
  // 性能优化字段
  preloaded?: boolean; // 是否已预加载
  cached?: boolean; // 是否已缓存
}

// 搜索索引类型
export interface SearchIndex {
  id: string;
  title: string;
  category: string;
  tags: string[];
  searchText: string; // 合并的搜索文本
  weight: number; // 权重分数
}

// 分类索引类型
export interface CategoryIndex {
  id: string;
  name: { [key: string]: string };
  gameIds: string[];
  gameCount: number;
  averageRating: number;
}

// 标签索引类型
export interface TagIndex {
  tag: string;
  gameIds: string[];
  gameCount: number;
  category: string[];
}

// 缓存管理器类型
export interface CacheManager {
  get<T>(key: string): T | null;
  set<T>(key: string, value: T, ttl?: number): void;
  delete(key: string): void;
  clear(): void;
  size(): number;
}

// 数据管理器配置
export const dataManagerConfig = {
  // 启用数据管理器
  enabled: true,
  // 自动初始化
  autoInit: true,
  // 后台更新
  backgroundUpdate: true,
  // 更新间隔（毫秒）
  updateInterval: 5 * 60 * 1000, // 5分钟
  // 错误重试次数
  retryAttempts: 3,
  // 重试延迟（毫秒）
  retryDelay: 1000,
};

export default gameDataConfig;
