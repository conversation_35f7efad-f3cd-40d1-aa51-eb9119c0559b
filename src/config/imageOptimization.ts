// 图片优化配置
export const imageOptimizationConfig = {
  // 代理设置
  proxy: {
    enabled: true,
    allowedDomains: [
      'www.onlinegames.io',
      'onlinegames.io',
      'images.unsplash.com',
      'avatars.githubusercontent.com'
    ],
    defaultQuality: 85,
    cacheMaxAge: 31536000, // 1年
  },

  // 图片尺寸配置
  sizes: {
    gameCard: {
      width: 320,
      height: 180,
      quality: 85,
      sizes: '(max-width: 640px) 320px, (max-width: 768px) 240px, (max-width: 1024px) 280px, 320px'
    },
    gameHero: {
      width: 1280,
      height: 720,
      quality: 90,
      sizes: '(max-width: 640px) 640px, (max-width: 768px) 768px, (max-width: 1024px) 1024px, 1280px'
    },
    thumbnail: {
      width: 160,
      height: 90,
      quality: 75,
      sizes: '(max-width: 640px) 96px, (max-width: 768px) 128px, 160px'
    },
    avatar: {
      width: 64,
      height: 64,
      quality: 80,
      sizes: '64px'
    }
  },

  // 预加载策略
  preload: {
    // 首屏图片数量
    priorityCount: 4,
    // 高优先级图片数量
    highPriorityCount: 2,
    // 预加载延迟（毫秒）
    delay: 100,
    // 批量预加载间隔（毫秒）
    batchInterval: 200,
    // 用户空闲时间阈值（毫秒）
    idleThreshold: 2000,
  },

  // 懒加载设置
  lazyLoad: {
    // Intersection Observer 根边距
    rootMargin: '100px',
    // 优先图片的根边距
    priorityRootMargin: '200px',
    // 阈值
    threshold: 0.1,
  },

  // 缓存策略
  cache: {
    // Service Worker 缓存名称
    cacheName: 'freehubgames-images-v1',
    // 最大缓存条目数
    maxEntries: 1000,
    // 缓存过期时间（毫秒）
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
  },

  // 性能监控
  performance: {
    // 是否启用监控
    enabled: process.env.NODE_ENV === 'development',
    // 慢图片阈值（毫秒）
    slowImageThreshold: 1000,
    // 报告间隔（毫秒）
    reportInterval: 30000,
    // 最大记录数
    maxRecords: 1000,
  },

  // 占位符设置
  placeholder: {
    // 默认占位符颜色
    backgroundColor: '#f3f4f6',
    // 文字颜色
    textColor: '#9ca3af',
    // 默认文字
    text: 'Image not available',
    // 是否使用模糊占位符
    useBlur: true,
  },

  // 错误处理
  errorHandling: {
    // 最大重试次数
    maxRetries: 3,
    // 重试延迟（毫秒）
    retryDelay: 1000,
    // 是否显示错误占位符
    showErrorPlaceholder: true,
  }
};

// 根据设备类型获取优化配置
export function getOptimizedImageConfig(
  type: keyof typeof imageOptimizationConfig.sizes,
  isMobile: boolean = false,
  isHighDPI: boolean = false
) {
  const baseConfig = imageOptimizationConfig.sizes[type];
  
  // 移动设备优化
  if (isMobile) {
    return {
      ...baseConfig,
      width: Math.floor(baseConfig.width * 0.8),
      height: Math.floor(baseConfig.height * 0.8),
      quality: Math.max(baseConfig.quality - 10, 60),
    };
  }

  // 高DPI屏幕优化
  if (isHighDPI) {
    return {
      ...baseConfig,
      width: Math.floor(baseConfig.width * 1.5),
      height: Math.floor(baseConfig.height * 1.5),
      quality: Math.min(baseConfig.quality + 5, 95),
    };
  }

  return baseConfig;
}

// 生成代理URL
export function generateProxyUrl(
  originalUrl: string,
  width?: number,
  quality?: number
): string {
  if (typeof window === 'undefined') return originalUrl;
  
  // 检查是否是外部URL
  const isExternalUrl = originalUrl.startsWith('http') && 
    !originalUrl.includes(window.location.hostname);
  
  if (!isExternalUrl || !imageOptimizationConfig.proxy.enabled) {
    return originalUrl;
  }

  // 检查域名是否在允许列表中
  const url = new URL(originalUrl);
  const isAllowedDomain = imageOptimizationConfig.proxy.allowedDomains.some(
    domain => url.hostname.includes(domain)
  );

  if (!isAllowedDomain) {
    return originalUrl;
  }

  const params = new URLSearchParams({
    url: originalUrl,
    q: String(quality || imageOptimizationConfig.proxy.defaultQuality),
  });
  
  if (width) {
    params.set('w', String(width));
  }
  
  return `/api/image-proxy?${params.toString()}`;
}

// 检查是否应该使用代理
export function shouldUseProxy(url: string): boolean {
  if (!imageOptimizationConfig.proxy.enabled) return false;
  
  try {
    const urlObj = new URL(url);
    return imageOptimizationConfig.proxy.allowedDomains.some(
      domain => urlObj.hostname.includes(domain)
    );
  } catch {
    return false;
  }
}

// 生成响应式图片sizes属性
export function generateResponsiveSizes(breakpoints: Record<string, number>): string {
  return Object.entries(breakpoints)
    .map(([breakpoint, size]) => `(max-width: ${breakpoint}) ${size}px`)
    .join(', ');
}

// 预设的响应式尺寸
export const responsiveSizes = {
  gameCard: generateResponsiveSizes({
    '640px': 320,
    '768px': 240,
    '1024px': 280,
    '1280px': 320,
  }),
  gameHero: generateResponsiveSizes({
    '640px': 640,
    '768px': 768,
    '1024px': 1024,
    '1280px': 1280,
  }),
  thumbnail: generateResponsiveSizes({
    '640px': 96,
    '768px': 128,
    '1024px': 160,
  }),
};

export default imageOptimizationConfig;
