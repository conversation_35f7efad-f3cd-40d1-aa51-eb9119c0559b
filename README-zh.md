# FreeHubGames 🎮

**终极免费在线游戏平台**

FreeHubGames 是一个基于 Next.js 15 构建的现代化全功能在线游戏平台，提供 500+ 款免费浏览器游戏，涵盖多个游戏分类。通过我们的响应式设计、多语言支持和用户友好界面，体验无缝游戏乐趣。

[![在线演示](https://img.shields.io/badge/在线演示-brightgreen)](https://freehubgames.com)
[![Next.js](https://img.shields.io/badge/Next.js-15.0.3-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue)](https://www.typescriptlang.org/)
[![许可证](https://img.shields.io/badge/许可证-MIT-yellow.svg)](LICENSE)

> **📖 文档语言**: [English](README.md) | [中文文档](README-zh.md)

## 🌟 核心特性

### 🎮 游戏体验
- **500+ 免费游戏**：涵盖 8 大分类的丰富游戏库（动作、冒险、竞速、射击、益智、策略、体育、模拟）
- **即点即玩**：无需下载，直接在浏览器中畅玩
- **全屏模式**：沉浸式游戏体验，支持全屏游戏
- **游戏发现**：先进的分类和搜索功能
- **移动端优化**：响应式设计，完美适配移动设备

### 🌐 平台功能
- **多语言支持**：基于 next-intl 的完整国际化方案（中英文）
- **用户认证**：通过 NextAuth.js 实现的安全 OAuth 登录（Google 和 GitHub）
- **高级功能**：基于 Stripe 的订阅系统，提供增强游戏体验
- **SEO 优化**：自动生成站点地图和元标签优化
- **现代化 UI**：基于 Radix UI 和 Tailwind CSS 构建的精美界面
- **极致性能**：Next.js 15 和 Turbopack 提供闪电般的加载速度

## 🛠️ 技术栈

### 前端技术
- **框架**：Next.js 15.0.3 with App Router
- **编程语言**：TypeScript 5.x 提供类型安全
- **UI 库**：React 18.2.0
- **样式**：Tailwind CSS 3.4.1 配合自定义动画
- **组件库**：Radix UI 原语组件（Dialog、Dropdown、Accordion）
- **图标**：Lucide React 图标库
- **动画**：Framer Motion 提供流畅交互

### 后端与数据库
- **API**：Next.js API Routes 配合 TypeScript
- **数据库**：PostgreSQL 配合 Prisma ORM 6.1.0
- **身份认证**：NextAuth.js v4 配合 OAuth 提供商
- **支付系统**：Stripe 17.5.0 集成
- **文件存储**：Next.js Image 优化图片处理

### 开发与部署
- **构建工具**：Turbopack 提供超快开发体验
- **国际化**：next-intl 3.26.3 多语言支持
- **SEO**：next-sitemap 自动生成站点地图
- **部署**：Vercel 优化配合自动 CI/CD
- **包管理器**：pnpm 高效依赖管理

## 📋 环境要求

- **Node.js**：18.17 或更高版本
- **包管理器**：pnpm 8.0+（推荐）或 npm/yarn
- **数据库**：PostgreSQL 12+（推荐）
- **Git**：用于版本控制

## 🚀 快速开始

### 1. 克隆仓库

```bash
git clone https://github.com/wenhaofree/game-grove-web.git
cd game-grove-web
```

### 2. 安装依赖

```bash
pnpm install
# 或者
npm install
```

### 3. 环境变量配置

创建环境变量文件：

```bash
cp .env.example .env.local
```

配置以下环境变量：

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `DATABASE_URL` | PostgreSQL 数据库连接字符串 | `postgresql://user:pass@localhost:5432/freehubgames` |
| `NEXTAUTH_SECRET` | NextAuth.js 密钥 | `your-super-secret-key` |
| `NEXTAUTH_URL` | 应用程序 URL | `http://localhost:3000` |
| `AUTH_GOOGLE_ID` | Google OAuth 客户端 ID | `your-google-client-id` |
| `AUTH_GOOGLE_SECRET` | Google OAuth 客户端密钥 | `your-google-client-secret` |
| `AUTH_GITHUB_ID` | GitHub OAuth 应用 ID | `your-github-app-id` |
| `AUTH_GITHUB_SECRET` | GitHub OAuth 应用密钥 | `your-github-app-secret` |
| `STRIPE_PUBLIC_KEY` | Stripe 可发布密钥 | `pk_test_...` |
| `STRIPE_PRIVATE_KEY` | Stripe 私密密钥 | `sk_test_...` |
| `STRIPE_WEBHOOK_SECRET` | Stripe Webhook 端点密钥 | `whsec_...` |

### 4. 数据库设置

初始化 PostgreSQL 数据库：

```bash
# 生成 Prisma 客户端
pnpm db:generate

# 推送数据库架构
pnpm db:push

# （可选）打开 Prisma Studio 管理数据
pnpm db:studio
```

### 5. 启动开发服务器

```bash
pnpm dev
```

🎉 **成功！** 打开 [http://localhost:3000](http://localhost:3000) 查看本地运行的 FreeHubGames。

## 🎮 游戏分类

我们的平台提供 8 大主要分类的游戏：

- **🎯 动作游戏**：快节奏游戏，包括射击、格斗和街机风格游戏
- **🗺️ 冒险游戏**：故事驱动的游戏，包含探索和解谜元素
- **🏎️ 竞速游戏**：高速赛车游戏和驾驶模拟器
- **🔫 射击游戏**：第一人称和第三人称射击游戏
- **🧩 益智游戏**：挑战逻辑和解决问题能力的益智游戏
- **⚔️ 策略游戏**：需要规划和战略思维的战术游戏
- **⚽ 体育游戏**：虚拟体育游戏，包括足球、篮球等
- **🎯 模拟游戏**：生活模拟和管理类游戏

## 📜 可用脚本

### 开发命令
```bash
# 使用 Turbopack 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start

# 运行 ESLint 代码分析
pnpm lint
```

### 数据库命令
```bash
# 生成 Prisma 客户端
pnpm db:generate

# 推送架构变更到数据库
pnpm db:push

# 从现有数据库拉取架构
pnpm db:pull

# 打开 Prisma Studio（数据库 GUI）
pnpm db:studio

# 同步数据库架构（拉取 + 推送 + 生成）
pnpm db:sync
```

## 🚀 部署

### 部署到 Vercel（推荐）

[![使用 Vercel 部署](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwenhaofree%2Fgame-grove-web&env=DATABASE_URL,NEXTAUTH_SECRET,NEXTAUTH_URL,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET&project-name=freehubgames&repository-name=freehubgames)

**分步部署指南：**

1. **Fork 仓库** 到您的 GitHub 账户
2. **创建新项目** 在 [Vercel](https://vercel.com)
3. **导入您的 Fork 仓库**
4. **配置环境变量**（参见环境变量配置部分）
5. **部署** - Vercel 将自动构建和部署您的应用程序

### 其他部署选项

- **Netlify**：兼容静态导出
- **Railway**：全栈部署配合数据库
- **DigitalOcean App Platform**：基于容器的部署
- **自托管**：支持 Docker 部署

## 📁 项目结构

```
game-grove-web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # 国际化路由
│   │   │   ├── category/      # 游戏分类页面
│   │   │   ├── game/          # 单个游戏页面
│   │   │   └── page.tsx       # 首页
│   │   ├── api/               # API 路由
│   │   │   ├── auth/          # 身份认证端点
│   │   │   ├── orders/        # 支付/订单管理
│   │   │   └── stripe/        # Stripe Webhook 处理器
│   │   └── auth/              # 身份认证页面
│   ├── components/            # React 组件
│   │   ├── ui/                # 可复用 UI 组件
│   │   ├── sections/          # 页面区块
│   │   └── shared/            # 共享组件
│   ├── data/                  # 游戏数据和配置
│   ├── lib/                   # 工具函数和配置
│   └── types/                 # TypeScript 类型定义
├── prisma/                    # 数据库架构和迁移
├── public/                    # 静态资源（图片、图标等）
├── messages/                  # 国际化文件
└── package.json              # 依赖和脚本
```

## 🌐 多语言文档

本项目支持多种语言以提供更好的可访问性：

- **English**: [README.md](README.md) - 完整的英文文档
- **中文**: [README-zh.md](README-zh.md) - 完整的中文文档

我们欢迎翻译成其他语言！如果您想贡献翻译，请：
1. 创建一个带有适当语言代码的新 README 文件（例如，日语使用 `README-ja.md`）
2. 翻译所有部分，同时保持相同的结构
3. 提交包含您翻译的 Pull Request

## 🤝 贡献指南

我们欢迎对 FreeHubGames 的贡献！以下是您可以帮助的方式：

### 开发工作流程
1. **Fork** 仓库
2. **创建** 功能分支：`git checkout -b feature/amazing-feature`
3. **提交** 您的更改：`git commit -m 'Add amazing feature'`
4. **推送** 到分支：`git push origin feature/amazing-feature`
5. **提交** Pull Request

### 贡献指南
- 遵循现有的代码风格和约定
- 为新功能添加测试（如适用）
- 为任何新功能更新文档
- 确保所有测试在提交 PR 前通过
- 使用有意义的提交消息

### 添加新游戏
要向平台添加新游戏：
1. 在 `src/data/games.ts` 中更新游戏数据
2. 确保正确的分类和国际化
3. 添加适当的元数据和评分
4. 彻底测试游戏集成

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系与支持

- **作者**：WenHaoFree
- **邮箱**：<EMAIL>
- **GitHub**：[@wenhaofree](https://github.com/wenhaofree)
- **网站**：[FreeHubGames.com](https://freehubgames.com)

### 社区
- 🐛 **错误报告**：[GitHub Issues](https://github.com/wenhaofree/game-grove-web/issues)
- 💡 **功能请求**：[GitHub Discussions](https://github.com/wenhaofree/game-grove-web/discussions)
- 📧 **商务咨询**：<EMAIL>

---

<div align="center">

**⭐ 如果您觉得这个项目有帮助，请给我们一个星标！**

由 [WenHaoFree](https://github.com/wenhaofree) 用 ❤️ 制作

</div>



# TODO :
1. 增加数据库支持
2. 页面布局标头信息改为左侧竖排 ✅
3. seo内容优化
4. 部署：美国服务器